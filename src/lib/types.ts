export type UserClassification = 'standard' | 'ai_agent' | 'guest' | 'bot';

export interface User {
  id: string;
  displayId?: string;
  name: string;
  avatar: string;
  status?: 'online' | 'busy' | 'offline' | 'away' | 'dnd'; // Added 'away' and 'dnd'
  statusMessage?: string; // Optional custom status message
  title?: string;
  classification?: UserClassification; // Global type of user
  about?: string; // Added about field for profile information
  settings?: UserSettings; // User-specific settings
}

export interface UserSettings {
  markAsReadDelaySecondsOverride?: number | null; // User's preference for delay (null means use workspace default)
  showMessageCountOverride?: boolean | null; // User's preference for showing message count (null means use workspace default)
  messageCountTypeOverride?: 'total' | 'today' | null; // User's preference for what the count represents (null means use workspace default)
  showUnreadBadgeOverride?: boolean | null; // User's preference for showing unread badge (null means use workspace default)
  // other user-specific preferences can go here
  themeOverride?: string | null; // User's preference for theme (null means use workspace default)
  channelViewOverrides?: Record<string, ChannelViewKey[]>; // User's specific view preferences per channel
  showStatusIndicatorInMessagesOverride?: boolean | null; // User's preference for showing status indicators in messages
  projectViewOverrides?: Record<string, ProjectViewKey[]>; // User's specific view preferences per project
  keyboardNavigationModeOverride?: boolean | null; // User's preference for keyboard navigation mode
  autoResetNewMessagesOverride?: boolean | null; // User's preference for auto-resetting new message indicators
}

// Defines the available views for a channel
export type ChannelViewKey = 'Messages' | 'Topics' | 'Files' | 'Members' | 'Note';

// Defines the available views for a project
export type ProjectViewKey = 'Overview' | 'Topics' | 'Activity' | 'Analytics' | 'Files';

export interface Message {
  id: string;
  content: string;
  timestamp: string;
  userId: string;
  displayId?: string;
  author?: User; // Changed from profiles to author
  reactions?: Reaction[];
  reactions_summary?: ReactionSummary[]; // New field for aggregated reactions
  editedTimestamp?: string; // Changed from edited?: boolean to store the actual timestamp
  threadId?: string;
  files?: File[]; // This will eventually hold File objects from public.files
  channelId?: string; // Refers to the main channel the message is in
  topicId?: string; // Refers to the specific topic (formerly subTopic/ChannelTopic) within the channel
  alsoSendToChannel?: boolean; // If true, a thread reply also appears in the main channel
}

export interface Thread {
  id: string;
  displayId?: string;
  parentMessageId: string;
  messages: Message[];
}

// Renamed to Channel throughout the application
export interface Channel {
  id: string;
  displayId?: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  members: string[];
  messages: Message[];
  threads: Record<string, Thread>;
  createdAt: string;
  channelTopics?: ChannelTopic[]; // Topics within the channel
  files?: File[]; // All files in the channel
  pinnedFiles?: string[]; // IDs of pinned files
  channelNote?: string; // Collaborative note content
  activeChannelTopicId?: string; // ID of the currently active/focused ChannelTopic (references an ID from channelTopics)
  unreadCount?: number; // Number of unread messages for the current user
  lastMessageTimestamp?: string; // Timestamp of the most recent message in the channel
  channelSpecificDefaultViews?: ChannelViewKey[]; // Optional: Workspace admin override for this channel's default views
  views?: ChannelViewKey[]; // Available views for this channel
  isUnreadCountPlaceholder?: boolean;
  oldestFetchedMessageTimestamp?: string | null;
  hasMoreOlderMessages?: boolean;
  last_fetched_message_timestamp?: string | null;
  oldestFetchedMessageId?: string | null; // For keyset pagination
  newestFetchedMessageId?: string | null; // For keyset pagination (delta sync)
  // Fields for Dexie caching relationships
  workspace_id?: string;
  section_id?: string;
}

export interface ChannelTopic {
  id: string;
  channel_id: string; // Added
  title: string;
  summary: string;
  // messageIds: string[]; // References to messages in this topic - This might be fetched separately or as part of a more detailed topic view
  created_by: string; // Added - UUID of the user who created the topic
  created_at: string; // Renamed from createdAt for consistency
  updated_at?: string | null; // Added
  display_order?: number | null; // Added
  displayId?: string;
  is_archived: boolean; // Added
  archived_at?: string | null; // Added
  archived_by?: string | null; // Added - UUID of the user who archived the topic
}

export interface Section {
  id: string;
  displayId?: string;
  name: string;
  channels: Channel[]; // Groups channels
  sectionSpecificDefaultViews?: ProjectViewKey[]; // Optional: Workspace admin override for this section's default views
  // Field for Dexie caching relationships
  workspace_id?: string;
  display_order?: number; // For Dexie caching and sorting
}

export interface DirectMessage {
  id: string;
  displayId?: string;
  name?: string; // Optional custom name, primarily for group DMs
  participants: string[];
  messages: Message[];
  threads: Record<string, Thread>;
  topics?: ChannelTopic[]; // Added topics for DMs
  activeDmTopicId?: string; // Optional: ID of the active topic within this DM
  createdAt?: string;         // When the DM conversation was initiated
  unreadCount?: number; // Number of unread messages for the current user
  lastMessageTimestamp?: string; // Timestamp of the most recent message in the DM
  isUnreadCountPlaceholder?: boolean;
  oldestFetchedMessageTimestamp?: string | null;
  hasMoreOlderMessages?: boolean;
  last_fetched_message_timestamp?: string | null;
  oldestFetchedMessageId?: string | null; // For keyset pagination
  newestFetchedMessageId?: string | null; // For keyset pagination (delta sync)
}

export interface Reaction {
  emoji: string;
  count: number;
  users: string[];
}

export interface ReactionSummary {
  emoji: string;
  count: number;
  user_ids_array: string[];
}

export interface File { // This represents the public.files table structure
  id: string; // uuid from DB
  name: string;
  type: string; // MIME type
  url: string; // Supabase storage URL or temporary base64/text content in Phase 1/2
  size_bytes: number; // Renamed from size to match DB
  uploaded_by_user_id: string; // Renamed from uploadedBy
  created_at: string; // Renamed from timestamp
  message_id?: string | null;
  channel_id?: string | null;
  is_pinned_in_channel_id?: string | null; // if pinned in a specific channel
  // Client-side only, not in DB:
  isPinned?: boolean; // General pinned status, might be derived
  isStarred?: boolean;
  displayId?: string;
}

export type WorkspaceUserRole = 'admin' | 'member';

export interface WorkspaceDisplayUser extends User { // This should now correctly reference the User interface
  workspaceRole: WorkspaceUserRole;
}

export interface Workspace {
  id: string;
  displayId?: string;
  name: string;
  iconUrl?: string; // Changed from icon to iconUrl
  users: WorkspaceDisplayUser[];
  sections: Section[];
  directMessages: DirectMessage[];
  currentUserId: string;
  currentSectionId: string | null;
  currentChannelId: string | null; // ID of the currently selected Channel
  currentDirectMessageId: string | null;
  activeThreadId: string | null;
  createdAt?: string;
  settings?: WorkspaceSettings;
  owner_id?: string; // Added owner_id to match database schema and plan
  userWorkspaces?: WorkspaceSummary[]; // Added for the list of workspaces a user belongs to
}

export interface WorkspaceSummary {
  id: string;
  name: string;
  iconUrl?: string;
}

export interface WorkspaceSettings {
  defaultSectionId?: string;
  allowGuestInvites?: boolean;
  retentionDays?: number;
  theme?: string;
  colorMode?: 'light' | 'dark' | 'auto'; // Color mode preference (default: 'auto')
  markAsReadDelaySecondsDefault?: number; // Workspace-level default for read delay
  showMessageCountDefault?: boolean; // Workspace default for showing message count
  messageCountTypeDefault?: 'total' | 'today'; // Workspace default for what the count represents
  showUnreadBadgeDefault?: boolean; // Workspace default for showing unread badge
  defaultChannelViews?: ChannelViewKey[]; // Workspace-level default visible views for channels
  defaultProjectViews?: ProjectViewKey[]; // Workspace-level default visible views for projects
  showStatusIndicatorInMessagesDefault?: boolean; // Workspace default for showing status indicators in messages
  showTopBar?: boolean; // Workspace default for showing the top bar
  showProjectViewDefault?: boolean; // Workspace default for showing project view
  keyboardNavigationMode?: boolean; // Whether keyboard navigation mode is enabled (default: true)
  suppressRealtimeConnectionToast?: boolean; // New setting
  fontSize?: number;
  emailNotifications?: boolean;
  desktopNotifications?: boolean;
  mobileNotifications?: boolean;
  initialMessageFetchLimit?: number; // How many messages to fetch initially when opening a channel/DM (default: 25)
  olderMessageFetchLimit?: number; // How many older messages to fetch when scrolling up (default: 25)
  deltaMessageFetchLimit?: number; // How many newer messages to fetch during delta sync (default: 50)
  defaultReactionEmojis?: string[]; // Workspace-configurable default emoji set for quick reactions (default: ['👍', '❤️', '🎉'])
  hideInaccessiblePrivateChannels?: boolean; // Whether to hide private channels that user cannot access from navigation (sidebar, quick nav, etc.) (default: false)
}

export type PersistentUnreadInfo = Record<string, {
  unreadCount: number;
  lastReadMessageTimestamp?: string;
}>;

export interface UserConversationReadState {
  user_id: string; // UUID
  conversation_id: string; // UUID
  conversation_type: 'channel' | 'dm';
  last_read_message_timestamp?: string | null; // TIMESTAMPTZ
}

// For Phase 1 of File & Image UX Improvement Plan
export interface LocalAttachment {
  id: string; // local temporary ID, e.g., generated by uuid()
  name: string;
  type: string; // MIME type
  size: number; // in bytes
  dataUrl?: string; // For base64 encoded images
  textContent?: string; // For plain text file content
  fileObject?: globalThis.File; // Original browser File object, for future Supabase upload
  // For Phase 3:
  // uploadProgress?: number;
  // status?: 'pending_upload' | 'uploading' | 'complete' | 'error';
  // finalFileId?: string; // ID from public.files after successful upload
  // finalUrl?: string; // Supabase storage URL after successful upload
}
