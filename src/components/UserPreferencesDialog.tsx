import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator'; // Added Separator import
import { Switch } from '@/components/ui/switch'; // Added Switch, though might use RadioGroup
import { ScrollArea } from '@/components/ui/scroll-area'; // Added ScrollArea import
import { ThemeSelectionGrid } from '@/components/ui/ThemeSelectionGrid'; // Import ThemeSelectionGrid
import { useApp } from '@/lib/app-context';
import { UserSettings } from '@/lib/types';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface UserPreferencesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const UserPreferencesDialog: React.FC<UserPreferencesDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { getCurrentUser, updateUserSetting, workspaceSettings } = useApp();
  const currentUser = getCurrentUser();

  // Workspace defaults (provide sensible fallbacks if not set)
  const wsShowMsgCountDefault = workspaceSettings?.showMessageCountDefault ?? true;
  const wsMsgCountTypeDefault = workspaceSettings?.messageCountTypeDefault ?? 'total';
  const wsShowUnreadBadgeDefault = workspaceSettings?.showUnreadBadgeDefault ?? true;
  const wsMarkAsReadDelayDefault = workspaceSettings?.markAsReadDelaySecondsDefault ?? 5;
  const wsThemeDefault = workspaceSettings?.theme ?? 'default';
  const wsColorModeDefault = 'auto'; // Default to auto for all users
  const wsShowStatusIndicatorInMessagesDefault = workspaceSettings?.showStatusIndicatorInMessagesDefault ?? true;
  const wsKeyboardNavigationModeDefault = workspaceSettings?.keyboardNavigationMode ?? true;
  const wsAutoResetNewMessagesDefault = true; // Assuming true as default, can be added to workspace settings later

  const [markAsReadDelayOverride, setMarkAsReadDelayOverride] = useState<number>(
    currentUser?.settings?.markAsReadDelaySecondsOverride ?? wsMarkAsReadDelayDefault
  );
  const [showCountSetting, setShowCountSetting] = useState<boolean>(
    currentUser?.settings?.showMessageCountOverride ?? wsShowMsgCountDefault
  );
  const [countTypeSetting, setCountTypeSetting] = useState<'total' | 'today'>(
    currentUser?.settings?.messageCountTypeOverride ?? wsMsgCountTypeDefault
  );
  const [showUnreadBadgeOverride, setShowUnreadBadgeOverride] = useState<boolean>(
    currentUser?.settings?.showUnreadBadgeOverride ?? wsShowUnreadBadgeDefault
  );
  const [themeOverride, setThemeOverride] = useState<string>(
    currentUser?.settings?.themeOverride ?? wsThemeDefault
  );
  const [colorModeOverride, setColorModeOverride] = useState<'light' | 'dark' | 'auto' | null>(
    currentUser?.settings?.colorModeOverride ?? wsColorModeDefault
  );
  const [showStatusIndicatorInMessagesOverride, setShowStatusIndicatorInMessagesOverride] = useState<boolean>(
    currentUser?.settings?.showStatusIndicatorInMessagesOverride ?? wsShowStatusIndicatorInMessagesDefault
  );
  const [keyboardNavigationModeOverride, setKeyboardNavigationModeOverride] = useState<boolean>(
    currentUser?.settings?.keyboardNavigationModeOverride ?? wsKeyboardNavigationModeDefault
  );
  const [autoResetNewMessagesOverride, setAutoResetNewMessagesOverride] = useState<boolean>(
    currentUser?.settings?.autoResetNewMessagesOverride ?? wsAutoResetNewMessagesDefault
  );


  useEffect(() => {
    if (open) {
      if (currentUser?.settings) {
        setMarkAsReadDelayOverride(currentUser.settings.markAsReadDelaySecondsOverride ?? wsMarkAsReadDelayDefault);
        setShowCountSetting(currentUser.settings.showMessageCountOverride ?? wsShowMsgCountDefault);
        setCountTypeSetting(currentUser.settings.messageCountTypeOverride ?? wsMsgCountTypeDefault);
        setShowUnreadBadgeOverride(currentUser.settings.showUnreadBadgeOverride ?? wsShowUnreadBadgeDefault);
        setThemeOverride(currentUser.settings.themeOverride ?? wsThemeDefault);
        setColorModeOverride(currentUser.settings.colorModeOverride ?? wsColorModeDefault);
        setShowStatusIndicatorInMessagesOverride(currentUser.settings.showStatusIndicatorInMessagesOverride ?? wsShowStatusIndicatorInMessagesDefault);
        setKeyboardNavigationModeOverride(currentUser.settings.keyboardNavigationModeOverride ?? wsKeyboardNavigationModeDefault);
        setAutoResetNewMessagesOverride(currentUser.settings.autoResetNewMessagesOverride ?? wsAutoResetNewMessagesDefault);
      } else { // No current user settings, or no user
        setMarkAsReadDelayOverride(wsMarkAsReadDelayDefault);
        setShowCountSetting(wsShowMsgCountDefault);
        setCountTypeSetting(wsMsgCountTypeDefault);
        setShowUnreadBadgeOverride(wsShowUnreadBadgeDefault);
        setThemeOverride(wsThemeDefault);
        setColorModeOverride(wsColorModeDefault);
        setShowStatusIndicatorInMessagesOverride(wsShowStatusIndicatorInMessagesDefault);
        setKeyboardNavigationModeOverride(wsKeyboardNavigationModeDefault);
        setAutoResetNewMessagesOverride(wsAutoResetNewMessagesDefault);
      }
    }
  }, [open, currentUser?.settings, wsMarkAsReadDelayDefault, wsShowMsgCountDefault, wsMsgCountTypeDefault, wsShowUnreadBadgeDefault, wsThemeDefault, wsColorModeDefault, wsShowStatusIndicatorInMessagesDefault, wsKeyboardNavigationModeDefault, wsAutoResetNewMessagesDefault]);

  const handleSave = () => {
    if (currentUser) {
      const newSettings: UserSettings = {
        // Ensure we spread existing settings that are not managed by this dialog
        ...currentUser.settings,
        markAsReadDelaySecondsOverride: Number(markAsReadDelayOverride),
        showMessageCountOverride: showCountSetting,
        messageCountTypeOverride: countTypeSetting,
        showUnreadBadgeOverride: showUnreadBadgeOverride,
        themeOverride: themeOverride,
        colorModeOverride: colorModeOverride,
        showStatusIndicatorInMessagesOverride: showStatusIndicatorInMessagesOverride,
        keyboardNavigationModeOverride: keyboardNavigationModeOverride,
        autoResetNewMessagesOverride: autoResetNewMessagesOverride,
      };
      if (updateUserSetting) {
        updateUserSetting(currentUser.id, newSettings);
        toast.success('Preferences saved!');
      } else {
        toast.error('Could not save preferences. Function not available.');
      }
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl"> {/* Increased width to 2xl */}
        <DialogHeader>
          <DialogTitle>User Preferences</DialogTitle>
          <DialogDescription>
            Manage your personal settings for {currentUser?.name}.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[calc(100vh-220px)] pr-6"> {/* Adjusted max-h and added pr-6 */}
          <div className="py-4 space-y-6">

            {/* Theme Preference */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Appearance & Theme</Label>
              <div className="space-y-4 pl-2 border-l-2 border-muted">
                <div>
                  <Label className="font-normal mb-3 block">
                    Color Mode:
                  </Label>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      className={cn(
                        "py-2 px-3 text-sm border rounded-md transition-colors duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        colorModeOverride === 'light'
                          ? "bg-primary text-primary-foreground border-primary"
                          : "border-border"
                      )}
                      onClick={() => setColorModeOverride('light')}
                    >
                      Light
                    </button>
                    <button
                      className={cn(
                        "py-2 px-3 text-sm border rounded-md transition-colors duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        colorModeOverride === 'dark'
                          ? "bg-primary text-primary-foreground border-primary"
                          : "border-border"
                      )}
                      onClick={() => setColorModeOverride('dark')}
                    >
                      Dark
                    </button>
                    <button
                      className={cn(
                        "py-2 px-3 text-sm border rounded-md transition-colors duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        (colorModeOverride === 'auto' || colorModeOverride === null)
                          ? "bg-primary text-primary-foreground border-primary"
                          : "border-border"
                      )}
                      onClick={() => setColorModeOverride('auto')}
                    >
                      Auto
                    </button>
                  </div>
                </div>
                <div>
                  <Label className="font-normal">
                    Choose a specific theme:
                  </Label>
                  <ThemeSelectionGrid
                    selectedTheme={themeOverride}
                    onThemeChange={setThemeOverride}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Unread Badge and Timing Preferences */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Notifications & Unreads</Label>
              <div className="space-y-4 pl-2 border-l-2 border-muted">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="showUnreadBadge"
                    checked={showUnreadBadgeOverride}
                    onCheckedChange={setShowUnreadBadgeOverride}
                  />
                  <Label htmlFor="showUnreadBadge" className="font-normal">
                    Show unread indicator badge
                  </Label>
                </div>

                {/* Auto Reset New Messages Preference */}
                <div className="flex items-center space-x-3">
                  <Switch
                    id="autoResetNewMessages"
                    checked={autoResetNewMessagesOverride}
                    onCheckedChange={setAutoResetNewMessagesOverride}
                  />
                  <Label htmlFor="autoResetNewMessages" className="font-normal">
                    Auto-reset "New Messages" indicator
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground mt-1 pl-8"> {/* Adjusted padding to align with switch */}
                  When off, the "New Messages" line stays until you switch channels/DMs, even after the timeout.
                </p>

                {showUnreadBadgeOverride && (
                  <div className="space-y-2 mt-4">
                    <Label htmlFor="markAsReadDelayOverride" className={`font-normal ${!autoResetNewMessagesOverride ? 'text-muted-foreground' : ''}`}>
                      Mark messages as read after (seconds)
                    </Label>
                    <Input
                      id="markAsReadDelayOverride"
                      type="number"
                      value={String(markAsReadDelayOverride)}
                      onChange={(e) => {
                        const parsedValue = parseInt(e.target.value, 10);
                        setMarkAsReadDelayOverride(isNaN(parsedValue) || parsedValue < 0 ? wsMarkAsReadDelayDefault : parsedValue);
                      }}
                      min="0"
                      className={`w-32 ${!autoResetNewMessagesOverride ? 'opacity-50 cursor-not-allowed' : ''}`}
                      disabled={!autoResetNewMessagesOverride} // Disable input when autoReset is off
                    />
                    <p className={`text-xs mt-1 ${!autoResetNewMessagesOverride ? 'text-muted-foreground opacity-75' : 'text-muted-foreground'}`}>
                      0 for immediate. Determines when a channel is no longer "unread".
                      {!autoResetNewMessagesOverride && <span className="block text-xs text-[var(--app-destructive)] opacity-90">This setting has less effect when "Auto-reset New Messages" is off.</span>}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Message Count Display Preference */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Channel List Message Count</Label>
              <div className="space-y-4 pl-2 border-l-2 border-muted">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="showCountSetting"
                    checked={showCountSetting}
                    onCheckedChange={setShowCountSetting}
                  />
                  <Label htmlFor="showCountSetting" className="font-normal">
                    Show message count in channel list
                  </Label>
                </div>

                {/* Message Count Type Preference (conditionally displayed) */}
                {showCountSetting && (
                  <div className="space-y-2 pt-2">
                    <Label className="font-normal text-sm">Count type:</Label>
                    <RadioGroup
                      value={countTypeSetting}
                      onValueChange={(value) => setCountTypeSetting(value as 'total' | 'today')}
                      className="pl-3"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="total" id="countTypeTotal" />
                        <Label htmlFor="countTypeTotal" className="font-normal">Total Messages</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="today" id="countTypeToday" />
                        <Label htmlFor="countTypeToday" className="font-normal">Messages Today</Label>
                      </div>
                    </RadioGroup>
                  </div>
                )}
            </div>
          </div>

            <Separator />

            {/* Status Indicator in Messages Preference */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Message Display</Label>
              <div className="space-y-4 pl-2 border-l-2 border-muted">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="showStatusIndicatorInMessages"
                    checked={showStatusIndicatorInMessagesOverride}
                    onCheckedChange={setShowStatusIndicatorInMessagesOverride}
                  />
                  <Label htmlFor="showStatusIndicatorInMessages" className="font-normal">
                    Show user status indicator next to avatar in messages
                  </Label>
                </div>
              </div>
            </div>

            <Separator />

            {/* Keyboard Navigation Mode Preference */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Keyboard Navigation</Label>
              <div className="space-y-4 pl-2 border-l-2 border-muted">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="keyboardNavigationMode"
                    checked={keyboardNavigationModeOverride}
                    onCheckedChange={setKeyboardNavigationModeOverride}
                  />
                  <Label htmlFor="keyboardNavigationMode" className="font-normal">
                    Enable keyboard navigation mode
                  </Label>
                </div>
                <p className="text-xs text-muted-foreground mt-1 max-w-md">
                  When enabled, auto-focus on message inputs is disabled. Use the M key to focus the message input.
                  Press Shift+? or Ctrl+/ to see all keyboard shortcuts.
                </p>
              </div>
            </div>

        </div>
        </ScrollArea>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
