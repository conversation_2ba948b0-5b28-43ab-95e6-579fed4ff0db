import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator'; // Added Separator import
import { Switch } from '@/components/ui/switch'; // Added Switch, though might use RadioGroup
import { ScrollArea } from '@/components/ui/scroll-area'; // Added ScrollArea import
import { ThemeSelectionGrid } from '@/components/ui/ThemeSelectionGrid'; // Import ThemeSelectionGrid
import { useApp } from '@/lib/app-context';
import { UserSettings } from '@/lib/types';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface UserPreferencesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const UserPreferencesDialog: React.FC<UserPreferencesDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { getCurrentUser, updateUserSetting, workspaceSettings } = useApp();
  const currentUser = getCurrentUser();

  // Workspace defaults (provide sensible fallbacks if not set)
  const wsShowMsgCountDefault = workspaceSettings?.showMessageCountDefault ?? true;
  const wsMsgCountTypeDefault = workspaceSettings?.messageCountTypeDefault ?? 'total';
  const wsShowUnreadBadgeDefault = workspaceSettings?.showUnreadBadgeDefault ?? true;
  const wsMarkAsReadDelayDefault = workspaceSettings?.markAsReadDelaySecondsDefault ?? 5;
  const wsThemeDefault = workspaceSettings?.theme ?? 'default';
  const wsColorModeDefault = 'auto'; // Default to auto for all users
  const wsShowStatusIndicatorInMessagesDefault = workspaceSettings?.showStatusIndicatorInMessagesDefault ?? true;
  const wsKeyboardNavigationModeDefault = workspaceSettings?.keyboardNavigationMode ?? true;
  const wsAutoResetNewMessagesDefault = true; // Assuming true as default, can be added to workspace settings later

  const [markAsReadDelayOverride, setMarkAsReadDelayOverride] = useState<number>(
    currentUser?.settings?.markAsReadDelaySecondsOverride ?? wsMarkAsReadDelayDefault
  );
  const [showCountSetting, setShowCountSetting] = useState<boolean>(
    currentUser?.settings?.showMessageCountOverride ?? wsShowMsgCountDefault
  );
  const [countTypeSetting, setCountTypeSetting] = useState<'total' | 'today'>(
    currentUser?.settings?.messageCountTypeOverride ?? wsMsgCountTypeDefault
  );
  const [showUnreadBadgeOverride, setShowUnreadBadgeOverride] = useState<boolean>(
    currentUser?.settings?.showUnreadBadgeOverride ?? wsShowUnreadBadgeDefault
  );
  const [themeOverride, setThemeOverride] = useState<string>(
    currentUser?.settings?.themeOverride ?? wsThemeDefault
  );
  const [colorModeOverride, setColorModeOverride] = useState<'light' | 'dark' | 'auto' | null>(
    currentUser?.settings?.colorModeOverride ?? wsColorModeDefault
  );
  const [showStatusIndicatorInMessagesOverride, setShowStatusIndicatorInMessagesOverride] = useState<boolean>(
    currentUser?.settings?.showStatusIndicatorInMessagesOverride ?? wsShowStatusIndicatorInMessagesDefault
  );
  const [keyboardNavigationModeOverride, setKeyboardNavigationModeOverride] = useState<boolean>(
    currentUser?.settings?.keyboardNavigationModeOverride ?? wsKeyboardNavigationModeDefault
  );
  const [autoResetNewMessagesOverride, setAutoResetNewMessagesOverride] = useState<boolean>(
    currentUser?.settings?.autoResetNewMessagesOverride ?? wsAutoResetNewMessagesDefault
  );


  useEffect(() => {
    if (open) {
      if (currentUser?.settings) {
        setMarkAsReadDelayOverride(currentUser.settings.markAsReadDelaySecondsOverride ?? wsMarkAsReadDelayDefault);
        setShowCountSetting(currentUser.settings.showMessageCountOverride ?? wsShowMsgCountDefault);
        setCountTypeSetting(currentUser.settings.messageCountTypeOverride ?? wsMsgCountTypeDefault);
        setShowUnreadBadgeOverride(currentUser.settings.showUnreadBadgeOverride ?? wsShowUnreadBadgeDefault);
        setThemeOverride(currentUser.settings.themeOverride ?? wsThemeDefault);
        setColorModeOverride(currentUser.settings.colorModeOverride ?? wsColorModeDefault);
        setShowStatusIndicatorInMessagesOverride(currentUser.settings.showStatusIndicatorInMessagesOverride ?? wsShowStatusIndicatorInMessagesDefault);
        setKeyboardNavigationModeOverride(currentUser.settings.keyboardNavigationModeOverride ?? wsKeyboardNavigationModeDefault);
        setAutoResetNewMessagesOverride(currentUser.settings.autoResetNewMessagesOverride ?? wsAutoResetNewMessagesDefault);
      } else { // No current user settings, or no user
        setMarkAsReadDelayOverride(wsMarkAsReadDelayDefault);
        setShowCountSetting(wsShowMsgCountDefault);
        setCountTypeSetting(wsMsgCountTypeDefault);
        setShowUnreadBadgeOverride(wsShowUnreadBadgeDefault);
        setThemeOverride(wsThemeDefault);
        setColorModeOverride(wsColorModeDefault);
        setShowStatusIndicatorInMessagesOverride(wsShowStatusIndicatorInMessagesDefault);
        setKeyboardNavigationModeOverride(wsKeyboardNavigationModeDefault);
        setAutoResetNewMessagesOverride(wsAutoResetNewMessagesDefault);
      }
    }
  }, [open, currentUser?.settings, wsMarkAsReadDelayDefault, wsShowMsgCountDefault, wsMsgCountTypeDefault, wsShowUnreadBadgeDefault, wsThemeDefault, wsColorModeDefault, wsShowStatusIndicatorInMessagesDefault, wsKeyboardNavigationModeDefault, wsAutoResetNewMessagesDefault]);

  const handleSave = () => {
    if (currentUser) {
      const newSettings: UserSettings = {
        // Ensure we spread existing settings that are not managed by this dialog
        ...currentUser.settings,
        markAsReadDelaySecondsOverride: Number(markAsReadDelayOverride),
        showMessageCountOverride: showCountSetting,
        messageCountTypeOverride: countTypeSetting,
        showUnreadBadgeOverride: showUnreadBadgeOverride,
        themeOverride: themeOverride,
        colorModeOverride: colorModeOverride,
        showStatusIndicatorInMessagesOverride: showStatusIndicatorInMessagesOverride,
        keyboardNavigationModeOverride: keyboardNavigationModeOverride,
        autoResetNewMessagesOverride: autoResetNewMessagesOverride,
      };
      if (updateUserSetting) {
        updateUserSetting(currentUser.id, newSettings);
        toast.success('Preferences saved!');
      } else {
        toast.error('Could not save preferences. Function not available.');
      }
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl">User Preferences</DialogTitle>
          <DialogDescription className="text-base">
            Customize your personal settings and preferences for this workspace.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[calc(100vh-240px)] pr-6">
          <div className="py-2 space-y-8">

            {/* Theme Preference */}
            <div className="space-y-6">
              <div>
                <Label className="text-lg font-semibold">Appearance & Theme</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Customize how the app looks and feels
                </p>
              </div>

              <div className="space-y-6">
                {/* Color Mode Section */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">Color Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Choose between light, dark, or automatic based on your system preference
                  </p>
                  <div className="grid grid-cols-3 gap-3 max-w-md">
                    <button
                      className={cn(
                        "py-3 px-4 text-sm font-medium border rounded-lg transition-all duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        "flex items-center justify-center",
                        colorModeOverride === 'light'
                          ? "bg-primary text-primary-foreground border-primary shadow-sm"
                          : "border-border hover:border-muted-foreground/50"
                      )}
                      onClick={() => setColorModeOverride('light')}
                    >
                      ☀️ Light
                    </button>
                    <button
                      className={cn(
                        "py-3 px-4 text-sm font-medium border rounded-lg transition-all duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        "flex items-center justify-center",
                        colorModeOverride === 'dark'
                          ? "bg-primary text-primary-foreground border-primary shadow-sm"
                          : "border-border hover:border-muted-foreground/50"
                      )}
                      onClick={() => setColorModeOverride('dark')}
                    >
                      🌙 Dark
                    </button>
                    <button
                      className={cn(
                        "py-3 px-4 text-sm font-medium border rounded-lg transition-all duration-200",
                        "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        "flex items-center justify-center",
                        (colorModeOverride === 'auto' || colorModeOverride === null)
                          ? "bg-primary text-primary-foreground border-primary shadow-sm"
                          : "border-border hover:border-muted-foreground/50"
                      )}
                      onClick={() => setColorModeOverride('auto')}
                    >
                      🔄 Auto
                    </button>
                  </div>
                </div>

                {/* Theme Selection Section */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Theme</Label>
                    <p className="text-sm text-muted-foreground">
                      Select a color theme for the interface
                    </p>
                  </div>
                  <div className="bg-muted/30 rounded-lg p-4">
                    <ThemeSelectionGrid
                      selectedTheme={themeOverride}
                      onThemeChange={setThemeOverride}
                    />
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Unread Badge and Timing Preferences */}
            <div className="space-y-6">
              <div>
                <Label className="text-lg font-semibold">Notifications & Unreads</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Control how unread messages and notifications are displayed
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 rounded-lg border bg-card">
                  <Switch
                    id="showUnreadBadge"
                    checked={showUnreadBadgeOverride}
                    onCheckedChange={setShowUnreadBadgeOverride}
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="showUnreadBadge" className="text-sm font-medium cursor-pointer">
                      Show unread indicator badge
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Display a badge with unread message count next to channel names
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 rounded-lg border bg-card">
                  <Switch
                    id="autoResetNewMessages"
                    checked={autoResetNewMessagesOverride}
                    onCheckedChange={setAutoResetNewMessagesOverride}
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="autoResetNewMessages" className="text-sm font-medium cursor-pointer">
                      Auto-reset "New Messages" indicator
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      When off, the "New Messages" line stays until you switch channels/DMs, even after the timeout
                    </p>
                  </div>
                </div>

                {showUnreadBadgeOverride && (
                  <div className={cn(
                    "p-4 rounded-lg border bg-card space-y-3",
                    !autoResetNewMessagesOverride && "opacity-60"
                  )}>
                    <div>
                      <Label htmlFor="markAsReadDelayOverride" className="text-sm font-medium">
                        Mark messages as read after: {markAsReadDelayOverride} seconds
                      </Label>
                      <p className="text-xs text-muted-foreground mt-1">
                        How long to wait before marking messages as read when viewing a channel
                      </p>
                    </div>
                    <input
                      id="markAsReadDelayOverride"
                      type="range"
                      min="0"
                      max="30"
                      step="1"
                      value={markAsReadDelayOverride}
                      onChange={(e) => setMarkAsReadDelayOverride(Number(e.target.value))}
                      className="w-full h-2 bg-muted rounded-lg appearance-none cursor-pointer slider"
                      disabled={!autoResetNewMessagesOverride}
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Instant</span>
                      <span>30s</span>
                    </div>
                    {!autoResetNewMessagesOverride && (
                      <p className="text-xs text-amber-600 dark:text-amber-400">
                        This setting has less effect when "Auto-reset New Messages" is off
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Message Count Display Preference */}
            <div className="space-y-6">
              <div>
                <Label className="text-lg font-semibold">Channel List Display</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Customize what information appears in the channel list
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 rounded-lg border bg-card">
                  <Switch
                    id="showCountSetting"
                    checked={showCountSetting}
                    onCheckedChange={setShowCountSetting}
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="showCountSetting" className="text-sm font-medium cursor-pointer">
                      Show message count in channel list
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Display the number of messages next to each channel name
                    </p>
                  </div>
                </div>

                {showCountSetting && (
                  <div className="p-4 rounded-lg border bg-card space-y-3">
                    <Label className="text-sm font-medium">Count type</Label>
                    <RadioGroup
                      value={countTypeSetting}
                      onValueChange={(value) => setCountTypeSetting(value as 'total' | 'today')}
                      className="space-y-2"
                    >
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="total" id="countTypeTotal" />
                        <div>
                          <Label htmlFor="countTypeTotal" className="text-sm font-medium cursor-pointer">
                            Total Messages
                          </Label>
                          <p className="text-xs text-muted-foreground">Show total message count for each channel</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <RadioGroupItem value="today" id="countTypeToday" />
                        <div>
                          <Label htmlFor="countTypeToday" className="text-sm font-medium cursor-pointer">
                            Messages Today
                          </Label>
                          <p className="text-xs text-muted-foreground">Show only today's message count</p>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Status Indicator in Messages Preference */}
            <div className="space-y-6">
              <div>
                <Label className="text-lg font-semibold">Message Display</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Control how messages and user information are displayed
                </p>
              </div>

              <div className="flex items-start space-x-3 p-4 rounded-lg border bg-card">
                <Switch
                  id="showStatusIndicatorInMessages"
                  checked={showStatusIndicatorInMessagesOverride}
                  onCheckedChange={setShowStatusIndicatorInMessagesOverride}
                  className="mt-0.5"
                />
                <div className="space-y-1">
                  <Label htmlFor="showStatusIndicatorInMessages" className="text-sm font-medium cursor-pointer">
                    Show user status indicator next to avatar in messages
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Display online/offline status dots next to user avatars in the message list
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Keyboard Navigation Mode Preference */}
            <div className="space-y-6">
              <div>
                <Label className="text-lg font-semibold">Keyboard Navigation</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Configure keyboard shortcuts and navigation behavior
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 rounded-lg border bg-card">
                  <Switch
                    id="keyboardNavigationMode"
                    checked={keyboardNavigationModeOverride}
                    onCheckedChange={setKeyboardNavigationModeOverride}
                    className="mt-0.5"
                  />
                  <div className="space-y-1">
                    <Label htmlFor="keyboardNavigationMode" className="text-sm font-medium cursor-pointer">
                      Enable keyboard navigation mode
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      When enabled, auto-focus on message inputs is disabled. Use the M key to focus the message input.
                      Press Shift+? or Ctrl+/ to see all keyboard shortcuts.
                    </p>
                  </div>
                </div>
              </div>
            </div>

        </div>
        </ScrollArea>
        <DialogFooter className="pt-6 gap-3">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="px-6">
            Cancel
          </Button>
          <Button onClick={handleSave} className="px-6">
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
