import React, { useRef, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { Message } from './Message';
import { MessageInput } from './MessageInput';
import { X } from 'lucide-react';
// import { findUserById } from '@/lib/mock-data'; // Removed mock data import
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

export const ThreadPanel = () => {
  const { workspace, currentThread, setActiveThread, currentChannel, currentDirectMessage } = useApp(); // Added workspace
  const { selectedMessageId, setSelectedMessageId } = useKeyboardShortcuts();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // const inputRef = useRef<HTMLTextAreaElement>(null); // Removed as MessageInput handles its own focus via autoFocus
  const threadPanelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Scroll to bottom when thread changes
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    // Focusing is handled by MessageInput's autoFocus prop
    // and its internal useEffect for focusing the SimpleMDE editor.
  }, [currentThread]);

  // Auto-scroll when messages are added
  useEffect(() => {
    if (currentThread) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [currentThread?.messages.length]);

  // Add ESC key handler to close thread panel
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setActiveThread(null);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [setActiveThread]);

  if (!currentThread || !currentThread.messages || currentThread.messages.length === 0) {
    // If currentThread is null, or has no messages, panel cannot be displayed.
    return null;
  }

  // The parentMessageId is definitive. Find this message within the currentThread's own messages.
  const parentMessage = currentThread.messages.find(m => m.id === currentThread.parentMessageId);

  if (!parentMessage) {
    // This indicates an inconsistency: the parent message (identified by parentMessageId)
    // is not present in the currentThread's own list of messages.
    // This can happen if organizeMessagesIntoThreads couldn't find the parent in its input.
    // In this case, the ThreadPanel cannot render correctly.
    console.warn(`[ThreadPanel] Parent message with ID ${currentThread.parentMessageId} not found within currentThread.messages. Thread panel will not open.`);
    return null;
  }

  const parentUser = workspace?.users.find(u => u.id === parentMessage.userId);
  // Calculate replyCount based on messages other than the identified parentMessage.
  const replyCount = currentThread.messages.filter(m => m.id !== parentMessage.id).length;

  return (
    <div
      ref={threadPanelRef}
      className="w-full lg:w-80 xl:w-96 h-full flex flex-col lg:border-l border-[var(--app-border)] bg-[var(--app-main-bg)]"
      tabIndex={-1}
    >
      {/* Thread Header */}
      <div className="thread-header justify-between">
        <div className="flex items-center">
          <h3 className="font-medium text-[var(--app-main-text)]">Thread</h3>
          {parentUser && (
            <span className="text-xs ml-2 opacity-70">
              with {parentUser.name}
            </span>
          )}
        </div>
        <button
          onClick={() => setActiveThread(null)}
          className="p-1 rounded-md hover:bg-[var(--app-hover-bg)]"
          aria-label="Close thread"
          title="Close thread (Esc)"
          data-shortcut="Esc"
        >
          <X size={18} className="text-[var(--app-main-text)] opacity-70" />
        </button>
      </div>

      {/* Thread Messages */}
      <div className="flex-1 overflow-y-auto p-4 user-select-text">
        {/* Parent message shown in a more compact form */}
        <div className="mb-6 pb-3 border-b border-[var(--app-border)]">
          <div className="flex items-start">
            <img
              src={parentUser?.avatar}
              alt={parentUser?.name}
              className="w-8 h-8 rounded mr-3"
            />
            <div className="flex-1">
              <div className="flex items-baseline">
                <span className="message-sender font-bold">{parentUser?.name}</span>
                <span className="message-timestamp ml-2 text-xs">
                  {new Date(parentMessage.timestamp).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
              <div className="mt-1 text-[var(--app-main-text)] text-sm whitespace-pre-wrap thread-quote">
                {parentMessage.content}
              </div>
            </div>
          </div>
        </div>

        {/* Thread replies counter - always visible and sticky */}
        <div className="text-xs font-medium mb-4 sticky top-0 bg-[var(--app-main-bg)] py-2 border-b border-[var(--app-border)] z-10 text-[var(--app-main-text)] opacity-70">
          {replyCount > 0
            ? `${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`
            : 'No replies yet'}
        </div>

        {replyCount > 0 ? (
          <div className="space-y-6">
            {/* Replies with increased spacing */}
            {currentThread.messages.filter(message => message.id !== parentMessage.id).sort((a,b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()).map(message => (
              <Message
                key={message.id}
                message={message}
                isInThread={true}
              />
            ))}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          <div ref={messagesEndRef} />
        )}
      </div>

      {/* Thread Input */}
      <div className="p-3 border-t border-[var(--app-border)]">
        <MessageInput threadId={currentThread.id} placeholder="Reply in thread..." autoFocus={true} />
      </div>
    </div>
  );
};
