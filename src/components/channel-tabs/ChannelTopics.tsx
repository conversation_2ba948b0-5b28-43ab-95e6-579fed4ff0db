import { useState, useEffect, useMemo, useCallback } from 'react';
import { Channel, ChannelTopic } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { Plus, MessageCircle, ArrowLeft, Archive, ArchiveRestore, Trash2, Edit3, MoreVertical, Eye, EyeOff } from 'lucide-react';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { MessageList } from '../MessageList';
import { MessageInput } from '../MessageInput';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";


interface ChannelTopicsProps {
  channel: Channel;
}

export const ChannelTopics = ({ channel }: ChannelTopicsProps) => {
  const app = useApp();
  const { toast } = useToast();
  const [selectedTopic, setSelectedTopic] = useState<ChannelTopic | null>(null);
  const [highlightedTopicId, setHighlightedTopicId] = useState<string | null>(null);

  // Create Topic Dialog
  const [isCreateTopicDialogOpen, setIsCreateTopicDialogOpen] = useState(false);
  const [newTopicTitle, setNewTopicTitle] = useState("");
  const [newTopicSummary, setNewTopicSummary] = useState("");

  // Edit Topic Dialog
  const [isEditTopicDialogOpen, setIsEditTopicDialogOpen] = useState(false);
  const [editingTopic, setEditingTopic] = useState<ChannelTopic | null>(null);
  const [editTopicTitle, setEditTopicTitle] = useState("");
  const [editTopicSummary, setEditTopicSummary] = useState("");

  // Delete Topic Dialog
  const [isDeleteTopicDialogOpen, setIsDeleteTopicDialogOpen] = useState(false);
  const [topicToDelete, setTopicToDelete] = useState<ChannelTopic | null>(null);

  // Archive/Unarchive Topic Dialog
  const [isArchiveTopicDialogOpen, setIsArchiveTopicDialogOpen] = useState(false);
  const [topicToArchive, setTopicToArchive] = useState<ChannelTopic | null>(null);
  const [isUnarchiveTopicDialogOpen, setIsUnarchiveTopicDialogOpen] = useState(false);
  const [topicToUnarchive, setTopicToUnarchive] = useState<ChannelTopic | null>(null);

  const [showArchived, setShowArchived] = useState(false);

  const { clearActiveChannelTopicForChannel } = app;
  const { isKeyboardNavigationModeEnabled } = useKeyboardShortcuts();

  const topics = useMemo(() => {
    return (channel.channelTopics || []).filter(topic => {
      if (showArchived) return true;
      return !topic.is_archived;
    });
  }, [channel.channelTopics, showArchived]);


  // Group topics by date
  const groupedTopics: Record<string, ChannelTopic[]> = useMemo(() => {
    return topics.reduce((groups, topic) => {
    const date = new Date(topic.created_at).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(topic);
    return groups;
  }, {} as Record<string, ChannelTopic[]>);
}, [topics]); // Added topics dependency

  // Sort dates newest to oldest
  const sortedDates = useMemo(() => {
    return Object.keys(groupedTopics).sort((a, b) =>
      new Date(b).getTime() - new Date(a).getTime()
    );
  }, [groupedTopics]);

  useEffect(() => {
    if (channel?.activeChannelTopicId) {
      const topicToSelect = topics.find(t => t.id === channel.activeChannelTopicId);
      if (topicToSelect) {
        setSelectedTopic(topicToSelect);
        clearActiveChannelTopicForChannel(channel.id);
      } else {
        clearActiveChannelTopicForChannel(channel.id);
      }
    }
  }, [channel?.activeChannelTopicId, channel?.id, clearActiveChannelTopicForChannel, topics]);

  const handleCreateTopic = async () => {
    if (!newTopicTitle.trim()) {
      toast({
        title: "Error",
        description: "Topic title cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    const createdTopic = await app.createChannelTopic(channel.id, newTopicTitle.trim(), newTopicSummary.trim());

    if (createdTopic) {
      // Toast is already shown in app.createChannelTopic
      setNewTopicTitle("");
      setNewTopicSummary("");
      setIsCreateTopicDialogOpen(false);
      setSelectedTopic(createdTopic); // Optionally select the new topic
    }
    // Error handling is done within app.createChannelTopic
  };

  const handleEditTopic = async () => {
    if (!editingTopic || !editTopicTitle.trim()) {
      toast({
        title: "Error",
        description: "Topic title cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    const updatedTopic = await app.updateChannelTopic(editingTopic.id, {
      title: editTopicTitle.trim(),
      summary: editTopicSummary ? editTopicSummary.trim() : null,
    });

    if (updatedTopic) {
      // Toast is already shown in app.updateChannelTopic
      setIsEditTopicDialogOpen(false);
      setEditingTopic(null);
      // If the edited topic is the selected one, update it
      if (selectedTopic?.id === updatedTopic.id) {
        setSelectedTopic(updatedTopic);
      }
    }
  };

  const handleDeleteTopic = async () => {
    if (!topicToDelete) return;

    const success = await app.deleteChannelTopic(topicToDelete.id, channel.id);
    if (success) {
      toast({
        title: "Topic Deleted",
        description: `Topic "${topicToDelete.title}" has been deleted.`,
      });
      setIsDeleteTopicDialogOpen(false);
      setTopicToDelete(null);
      if (selectedTopic?.id === topicToDelete.id) {
        setSelectedTopic(null); // Clear selection if deleted topic was selected
      }
    }
  };

  const handleArchiveTopic = async () => {
    if (!topicToArchive) return;
    const archivedTopic = await app.archiveChannelTopic(topicToArchive.id, channel.id);
    if (archivedTopic) {
      // Toast is already shown in app.archiveChannelTopic
      setIsArchiveTopicDialogOpen(false);
      setTopicToArchive(null);
      if (selectedTopic?.id === archivedTopic.id && !showArchived) {
         setSelectedTopic(null); // Clear selection if archived topic was selected and not showing archived
      } else if (selectedTopic?.id === archivedTopic.id) {
        setSelectedTopic(archivedTopic); // Update selected topic with archived status
      }
    }
  };

  const handleUnarchiveTopic = async () => {
    if (!topicToUnarchive) return;
    const unarchivedTopic = await app.unarchiveChannelTopic(topicToUnarchive.id, channel.id);
    if (unarchivedTopic) {
      // Toast is already shown in app.unarchiveChannelTopic
      setIsUnarchiveTopicDialogOpen(false);
      setTopicToUnarchive(null);
       if (selectedTopic?.id === unarchivedTopic.id) {
        setSelectedTopic(unarchivedTopic); // Update selected topic with unarchived status
      }
    }
  };


  // Handle back to topics list
  const handleBackToTopics = useCallback(() => {
    setSelectedTopic(null);
  }, []);

  // Handle click on topic to view messages
  const handleTopicClick = (topic: ChannelTopic) => {
    setSelectedTopic(topic);
  };

  // Get a flat list of all topics for keyboard navigation
  const allTopics = useMemo(() => {
    return sortedDates.flatMap(date => groupedTopics[date]);
  }, [groupedTopics, sortedDates]);

  // Handle keyboard navigation for topics
  useEffect(() => {
    if (!isKeyboardNavigationModeEnabled || selectedTopic) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard navigation when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Check for modifier keys - don't handle navigation if Ctrl+Shift is pressed
      // This ensures we don't interfere with message navigation shortcuts
      const isCtrlShiftPressed = (e.ctrlKey || e.metaKey) && e.shiftKey;
      if (isCtrlShiftPressed) {
        return;
      }

      // Handle up/down arrow keys and k/j for topic navigation
      const isUpNavigation = e.key === 'ArrowUp' || e.key === 'k';
      const isDownNavigation = e.key === 'ArrowDown' || e.key === 'j';
      const isEnterKey = e.key === 'Enter';
      const isNewTopicKey = e.key === 'n' && !e.ctrlKey && !e.metaKey && !e.shiftKey;
      const isHomeKey = e.key === 'Home';
      const isEndKey = e.key === 'End';

      if (!isUpNavigation && !isDownNavigation && !isEnterKey && !isNewTopicKey && !isHomeKey && !isEndKey) {
        return;
      }

      e.preventDefault();

      // Handle new topic shortcut
      if (isNewTopicKey) {
        setIsCreateTopicDialogOpen(true);
        return;
      }

      // Handle Enter to open selected topic
      if (isEnterKey && highlightedTopicId) {
        const topicToOpen = allTopics.find(t => t.id === highlightedTopicId);
        if (topicToOpen) {
          setSelectedTopic(topicToOpen);
        }
        return;
      }

      // Handle Home key to go to first topic
      if (isHomeKey && allTopics.length > 0) {
        setHighlightedTopicId(allTopics[0].id);
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[0].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        return;
      }

      // Handle End key to go to last topic
      if (isEndKey && allTopics.length > 0) {
        const lastIndex = allTopics.length - 1;
        setHighlightedTopicId(allTopics[lastIndex].id);
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[lastIndex].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        return;
      }

      // Handle navigation between topics
      if (isUpNavigation || isDownNavigation) {
        if (!allTopics.length) return;

        // Find the index of the currently highlighted topic
        const currentIndex = highlightedTopicId
          ? allTopics.findIndex(t => t.id === highlightedTopicId)
          : -1;

        let nextIndex: number;
        if (isUpNavigation) {
          // Move to previous topic or stay at first
          nextIndex = currentIndex > 0 ? currentIndex - 1 : 0;
        } else {
          // Move to next topic or stay at last
          nextIndex = currentIndex < allTopics.length - 1 ? currentIndex + 1 : allTopics.length - 1;
        }

        // If no topic is highlighted yet, select the first or last depending on direction
        if (currentIndex === -1) {
          nextIndex = isUpNavigation ? allTopics.length - 1 : 0;
        }

        // Highlight the next topic
        setHighlightedTopicId(allTopics[nextIndex].id);

        // Scroll the topic into view
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[nextIndex].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [allTopics, highlightedTopicId, isKeyboardNavigationModeEnabled, selectedTopic]);

  // Render topic list view
  const renderTopicList = () => {
    return (
      <>
        <div className="flex items-center justify-between mb-6 px-4">
          <div className="flex items-center gap-2">
            <MessageCircle size={18} />
            <h2 className="font-semibold text-lg">Topics</h2>
            <Badge variant="outline" className="ml-1">{topics.length}</Badge>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center space-x-2">
              <Switch
                id="show-archived-topics"
                checked={showArchived}
                onCheckedChange={setShowArchived}
                aria-label={showArchived ? "Hide archived topics" : "Show archived topics"}
              />
              <Label htmlFor="show-archived-topics" className="text-sm">
                {showArchived ? <EyeOff size={16} className="inline mr-1" /> : <Eye size={16} className="inline mr-1" />}
                {showArchived ? 'Hide Archived' : 'Show Archived'}
              </Label>
            </div>
            <AlertDialog open={isCreateTopicDialogOpen} onOpenChange={setIsCreateTopicDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button
                  size="sm"
                  onClick={() => {
                    setNewTopicTitle("");
                    setNewTopicSummary("");
                    setIsCreateTopicDialogOpen(true);
                  }}
                  className="flex items-center gap-1"
                  title="New topic (N)"
                >
                  <Plus size={16} />
                  <span>New topic</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Create New Topic</AlertDialogTitle>
                  <AlertDialogDescription>
                    Enter a title and an optional summary for your new topic.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="topic-title" className="text-right">
                      Title
                    </Label>
                    <Input
                      id="topic-title"
                      value={newTopicTitle}
                      onChange={(e) => setNewTopicTitle(e.target.value)}
                      className="col-span-3"
                      placeholder="e.g., Q4 Planning"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="topic-summary" className="text-right">
                      Summary
                    </Label>
                    <Textarea
                      id="topic-summary"
                      value={newTopicSummary}
                      onChange={(e) => setNewTopicSummary(e.target.value)}
                      className="col-span-3"
                      placeholder="Optional: A brief description of the topic's purpose or content."
                    />
                  </div>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => { setNewTopicTitle(""); setNewTopicSummary(""); }}>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleCreateTopic}>Create Topic</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>

        {sortedDates.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">
              {showArchived && channel.channelTopics?.some(t => t.is_archived) ? "No archived topics found." : "No topics found in this channel."}
            </p>
          </div>
        ) : (
          sortedDates.map(date => (
            <div key={date} className="mb-6">
              <h3 className="text-xs uppercase font-semibold text-gray-500 mb-2 px-4">{date}</h3>
              {groupedTopics[date].map(topic => (
                <div
                  key={topic.id}
                  className={`flex items-center justify-between py-3 px-4 border-b border-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-none group ${
                    highlightedTopicId === topic.id ? 'bg-[var(--app-hover-bg)] ring-2 ring-[var(--app-highlight)]' : ''
                  } ${topic.is_archived ? 'opacity-60' : ''}`}
                  onMouseEnter={() => setHighlightedTopicId(topic.id)}
                  data-topic-id={topic.id}
                >
                  <Button
                    variant="ghost"
                    className="flex-grow justify-start h-auto font-normal p-0 text-left"
                    onClick={() => handleTopicClick(topic)}
                  >
                    <div className="flex flex-col items-start">
                      <h4 className="font-medium text-base flex items-center">
                        {topic.title}
                        {topic.is_archived && <Archive size={14} className="ml-2 text-gray-500" />}
                      </h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-[450px]">{topic.summary}</p>
                    </div>
                  </Button>
                  {/* <Badge variant="secondary" className="text-xs ml-2 self-center">
                    {topic.messageIds?.length || 0} messages
                  </Badge> */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="ml-2 h-8 w-8 opacity-0 group-hover:opacity-100 focus:opacity-100">
                        <MoreVertical size={16} />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => {
                          setEditingTopic(topic);
                          setEditTopicTitle(topic.title);
                          setEditTopicSummary(topic.summary || ''); // Convert null to empty string
                          setIsEditTopicDialogOpen(true);
                        }}
                        disabled={topic.is_archived}
                      >
                        <Edit3 size={14} className="mr-2" /> Edit
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {topic.is_archived ? (
                        <DropdownMenuItem
                          onClick={() => {
                            setTopicToUnarchive(topic);
                            setIsUnarchiveTopicDialogOpen(true);
                          }}
                        >
                          <ArchiveRestore size={14} className="mr-2" /> Unarchive
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem
                          onClick={() => {
                            setTopicToArchive(topic);
                            setIsArchiveTopicDialogOpen(true);
                          }}
                        >
                          <Archive size={14} className="mr-2" /> Archive
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem
                        onClick={() => {
                          setTopicToDelete(topic);
                          setIsDeleteTopicDialogOpen(true);
                        }}
                        className="text-red-600 dark:text-red-500 hover:!bg-red-50 dark:hover:!bg-red-900/50 hover:!text-red-700 dark:hover:!text-red-400"
                      >
                        <Trash2 size={14} className="mr-2" /> Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          ))
        )}

        {/* Edit Topic Dialog */}
        {editingTopic && (
          <AlertDialog open={isEditTopicDialogOpen} onOpenChange={setIsEditTopicDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Edit Topic</AlertDialogTitle>
                <AlertDialogDescription>
                  Update the title and summary for "{editingTopic.title}".
                </AlertDialogDescription>
              </AlertDialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-topic-title" className="text-right">
                    Title
                  </Label>
                  <Input
                    id="edit-topic-title"
                    value={editTopicTitle}
                    onChange={(e) => setEditTopicTitle(e.target.value)}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-topic-summary" className="text-right">
                    Summary
                  </Label>
                  <Textarea
                    id="edit-topic-summary"
                    value={editTopicSummary}
                    onChange={(e) => setEditTopicSummary(e.target.value)}
                    className="col-span-3"
                  />
                </div>
              </div>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setEditingTopic(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleEditTopic}>Save Changes</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        {/* Delete Topic Confirmation Dialog */}
        {topicToDelete && (
          <AlertDialog open={isDeleteTopicDialogOpen} onOpenChange={setIsDeleteTopicDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Topic?</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete the topic "{topicToDelete.title}"? Messages will no longer be associated with this topic but will remain in the channel. This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setTopicToDelete(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteTopic} className="bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800">Delete</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        {/* Archive Topic Confirmation Dialog */}
        {topicToArchive && (
          <AlertDialog open={isArchiveTopicDialogOpen} onOpenChange={setIsArchiveTopicDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Archive Topic?</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to archive the topic "{topicToArchive.title}"? This will hide it from the main list and also archive its associated messages.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setTopicToArchive(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleArchiveTopic}>Archive</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        {/* Unarchive Topic Confirmation Dialog */}
        {topicToUnarchive && (
          <AlertDialog open={isUnarchiveTopicDialogOpen} onOpenChange={setIsUnarchiveTopicDialogOpen}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Unarchive Topic?</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to unarchive the topic "{topicToUnarchive.title}"? This will make it visible again and unarchive its associated messages.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setTopicToUnarchive(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleUnarchiveTopic}>Unarchive</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </>
    );
  };

  // Add keyboard navigation for topic detail view
  useEffect(() => {
    if (!selectedTopic || !isKeyboardNavigationModeEnabled) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }
      const isCtrlShiftPressed = (e.ctrlKey || e.metaKey) && e.shiftKey;
      if (isCtrlShiftPressed) {
        return;
      }
      if (e.key === 'Escape') {
        e.preventDefault();
        handleBackToTopics();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedTopic, isKeyboardNavigationModeEnabled, handleBackToTopics]);


  if (selectedTopic) {
    return (
      <div className="h-full flex flex-col">
        <div className="border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBackToTopics}
              className="h-8 w-8 mr-2"
              title="Back to topics (Esc)"
            >
              <ArrowLeft size={16} />
            </Button>
            <h3 className="font-semibold text-lg flex items-center">
              {selectedTopic.title}
              {selectedTopic.is_archived && <Archive size={16} className="ml-2 text-gray-500" />}
            </h3>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400 ml-10">{selectedTopic.summary}</p>
        </div>
        <div className="flex-1">
          <ResizablePanelGroup direction="vertical" className="h-full">
            <ResizablePanel defaultSize={75} minSize={30}>
              <MessageList topicId={selectedTopic.id} />
            </ResizablePanel>
            <ResizableHandle className="bg-app-border hover:bg-app-highlight transition-colors" />
            <ResizablePanel defaultSize={25} minSize={15} maxSize={70}>
              <div className="p-4 h-full">
                <MessageInput topicId={selectedTopic.id} disabled={selectedTopic.is_archived} isInResizablePanel={true} />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    );
  } else {
    return (
      <ScrollArea className="h-full">
        <div className="p-4">
          {renderTopicList()}
        </div>
      </ScrollArea>
    );
  }
};
