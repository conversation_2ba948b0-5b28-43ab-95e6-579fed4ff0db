import React, { useState, useEffect } from 'react'; // Added useEffect
import { useApp } from '@/lib/app-context';
import { ChannelViewKey } from '@/lib/types'; // Added ChannelViewKey
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { Hash, Lock, User, Info, PanelLeft, Menu } from 'lucide-react'; // Added PanelLeft, Menu
import { Button } from '@/components/ui/button';
import { ChannelDetailsDialog } from './ChannelDetailsDialog';
import { MemberProfileDialog } from './MemberProfileDialog'; // Added
import { User as UserType } from '@/lib/types'; // Added
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { ChannelMembers } from './channel-tabs/ChannelMembers';
import { ChannelTopics } from './channel-tabs/ChannelTopics';
import { ChannelFiles } from './channel-tabs/ChannelFiles';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { ChannelNote } from './channel-tabs/ChannelNote';
import { ProjectView } from './project-view/ProjectView';
import { PrivateChannelMessage } from './PrivateChannelMessage';


const ALL_CHANNEL_VIEWS_ORDERED: ChannelViewKey[] = ['Messages', 'Topics', 'Files', 'Members', 'Note'];

export const MainContent = () => {
  const {
    currentChannel,
    currentDirectMessage,
    currentSection,
    getCurrentUser,
    workspace,
    toggleSidebar,
    isSidebarOpen,
    workspaceSettings,
    currentChannelActiveView, // Added
    setCurrentChannelActiveView // Added
  } = useApp();
  const currentUser = getCurrentUser();
  const showTopBar = workspaceSettings?.showTopBar ?? true; // Default to true if not set
  const showProjectView = workspaceSettings?.showProjectViewDefault ?? true; // Default to true if not set
  const [activeTab, setActiveTab] = useState<string>('messages'); // Stays as string (lowercase)
  const [displayableTabs, setDisplayableTabs] = useState<ChannelViewKey[]>(['Messages']);
  const [isChannelDetailsOpen, setIsChannelDetailsOpen] = useState(false);
  const [isMemberProfileOpen, setIsMemberProfileOpen] = useState(false); // Added
  const [selectedDmMember, setSelectedDmMember] = useState<UserType | null>(null); // Added
  // We're using the useKeyboardShortcuts hook to ensure keyboard shortcuts work
  useKeyboardShortcuts();

  useEffect(() => {
    if (currentChannel) {
      const userOverrides = currentUser.settings?.channelViewOverrides?.[currentChannel.id];
      const channelDefault = currentChannel.channelSpecificDefaultViews;
      const workspaceDefault = workspace.settings?.defaultChannelViews;

      let determinedViews: ChannelViewKey[] = [];

      if (userOverrides && userOverrides.length > 0) {
        determinedViews = userOverrides;
      } else if (channelDefault && channelDefault.length > 0) {
        determinedViews = channelDefault;
      } else if (workspaceDefault && workspaceDefault.length > 0) {
        determinedViews = workspaceDefault;
      } else {
        determinedViews = [...ALL_CHANNEL_VIEWS_ORDERED]; // Fallback to all views
      }

      // Ensure at least one view is always selected, defaulting to 'Messages'
      if (determinedViews.length === 0) {
        determinedViews = ['Messages'];
      }

      const newDisplayableTabs = ALL_CHANNEL_VIEWS_ORDERED.filter(view => determinedViews.includes(view));
      setDisplayableTabs(newDisplayableTabs);

      const currentActiveTabIsValid = newDisplayableTabs.some(tab => tab.toLowerCase() === activeTab);
      if (newDisplayableTabs.length > 0 && !currentActiveTabIsValid) {
        setActiveTab(newDisplayableTabs[0].toLowerCase());
      } else if (newDisplayableTabs.length === 0) { // Should ideally not be reached
        setActiveTab('messages');
      }
    } else {
      // Reset when no channel is selected (though tabs section won't render)
      setDisplayableTabs(['Messages']);
      setActiveTab('messages');
    }
  }, [currentChannel, currentUser.settings, workspace.settings, activeTab]);

  // Effect to sync local activeTab with context's currentChannelActiveView
  useEffect(() => {
    if (currentChannel && currentChannelActiveView) {
      const tabKey = currentChannelActiveView.toLowerCase();
      if (displayableTabs.map(t => t.toLowerCase()).includes(tabKey) && activeTab !== tabKey) {
        setActiveTab(tabKey);
      }
    }
    // If currentChannelActiveView is null (e.g., channel changed without specific tab nav),
    // the other useEffect (lines 28-65) will set a default activeTab.
  }, [currentChannel, currentChannelActiveView, displayableTabs, activeTab]);

  // Display the ProjectView when a section is selected but no channel or DM is selected,
  // only if showProjectView setting is enabled
  if (currentSection && !currentChannel && !currentDirectMessage && showProjectView) {
    return <ProjectView />;
  }

  // Check if user has access to private channel
  if (currentChannel && currentChannel.isPrivate && !currentChannel.members.includes(workspace.currentUserId)) {
    return (
      <div className="flex-1 flex flex-col overflow-hidden app-main-content">
        {/* Channel Header - still show it for context */}
        <div className="flex items-center justify-between px-6 py-3 border-b border-app-border">
          <div className="flex items-center min-w-0 text-[var(--app-main-text)]">
            {!showTopBar && (
              <Button
                onClick={toggleSidebar}
                variant="ghost"
                size="icon"
                className="h-8 w-8 mr-2 flex-shrink-0"
                title={isSidebarOpen ? "Hide sidebar (Ctrl+B)" : "Show sidebar (Ctrl+B)"}
              >
                {isSidebarOpen ? <PanelLeft size={18} /> : <Menu size={18} />}
              </Button>
            )}
            <div className="flex items-center shrink-0">
              <Lock size={16} />
              <h2 className="ml-2 font-semibold text-lg whitespace-nowrap">{currentChannel.name}</h2>
            </div>
          </div>
        </div>

        {/* Private Channel Message */}
        <PrivateChannelMessage
          channelName={currentChannel.name}
        />
      </div>
    );
  }

  if (!currentChannel && !currentDirectMessage) {
    return (
      // Use app-main-content for background and text color
      <div className="flex-1 flex items-center justify-center app-main-content">
        <div className="text-center">
          {/* Apply themed text color */}
          <h2 className="text-xl font-semibold text-[var(--app-main-text)] opacity-80 mb-2">Welcome to MyApp</h2>
          <p className="text-[var(--app-main-text)] opacity-60">Select a channel or direct chat to start chatting</p>
        </div>
      </div>
    );
  }

  return (
    // Use appi-main-content for background and text color
    <div className="flex-1 flex flex-col overflow-hidden app-main-content">
      {/* Channel/DM Header - Use themed border and text */}
      <div className="flex items-center justify-between px-6 py-3 border-b border-app-border">
        <div className="flex items-center min-w-0 text-[var(--app-main-text)]">
          {!showTopBar && (
            <Button
              onClick={toggleSidebar}
              variant="ghost"
              size="icon"
              className="h-8 w-8 mr-2 flex-shrink-0" // Added flex-shrink-0
              title={isSidebarOpen ? "Hide sidebar (Ctrl+B)" : "Show sidebar (Ctrl+B)"}
            >
              {isSidebarOpen ? <PanelLeft size={18} /> : <Menu size={18} />}
            </Button>
          )}
          {currentChannel && (
            <>
              <div className="flex items-center shrink-0">
                {currentChannel.isPrivate ? <Lock size={16} /> : <Hash size={16} />}
                <h2 className="ml-2 font-semibold text-lg whitespace-nowrap">{currentChannel.name}</h2>
              </div>
              {currentChannel.description && (
                <>
                  <span className="mx-2 opacity-50 shrink-0">|</span>
                  <p className="text-sm opacity-70 truncate min-w-0">{currentChannel.description}</p>
                </>
              )}
            </>
          )}
          {currentDirectMessage && (
            <>
              <User size={16} />
              <h2 className="ml-2 font-semibold text-lg text-[var(--app-main-text)]">
                {currentDirectMessage.participants
                  .filter(id => id !== workspace.currentUserId)
                  .map(id => workspace.users.find(user => user.id === id)?.name)
                  .join(', ')}
              </h2>
            </>
          )}
        </div>
        {currentChannel && (
          <Button variant="ghost" size="icon" onClick={() => setIsChannelDetailsOpen(true)} className="ml-2 flex-shrink-0 text-[var(--app-main-text)] hover:bg-[var(--app-hover-bg)]">
            <Info size={18} />
          </Button>
        )}
        {currentDirectMessage && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              if (currentDirectMessage.participants.length > 0) {
                // Assuming 1-on-1 DM for simplicity or taking the first other participant
                const otherParticipantId = currentDirectMessage.participants.find(id => id !== workspace.currentUserId);
                if (otherParticipantId) {
                  const member = workspace.users.find(user => user.id === otherParticipantId);
                  if (member) {
                    setSelectedDmMember(member);
                    setIsMemberProfileOpen(true);
                  }
                }
              }
            }}
            className="ml-2 flex-shrink-0 text-[var(--app-main-text)] hover:bg-[var(--app-hover-bg)]"
            title="View user profile"
          >
            <Info size={18} />
          </Button>
        )}
      </div>

      {/* Channel Details Dialog */}
      {currentChannel && (
        <ChannelDetailsDialog
          open={isChannelDetailsOpen}
          onOpenChange={setIsChannelDetailsOpen}
          topic={currentChannel}
        />
      )}

      {/* Member Profile Dialog for DMs */}
      {selectedDmMember && (
        <MemberProfileDialog
          member={selectedDmMember}
          open={isMemberProfileOpen}
          onOpenChange={(open) => {
            setIsMemberProfileOpen(open);
            if (!open) {
              setSelectedDmMember(null); // Clear selected member when dialog closes
            }
          }}
        />
      )}

      {/* Tab Navigation */}
      {currentChannel && (
        <Tabs
          value={activeTab}
          onValueChange={(newTabValue) => {
            setActiveTab(newTabValue);
            // Find the original ChannelViewKey (capitalized) to update context
            const newTabKey = ALL_CHANNEL_VIEWS_ORDERED.find(
              (cvk) => cvk.toLowerCase() === newTabValue
            );
            if (newTabKey) {
              setCurrentChannelActiveView(newTabKey);
            } else {
              // Fallback or error, though this shouldn't happen if newTabValue is from displayableTabs
              setCurrentChannelActiveView(null);
            }
          }}
          className="flex-1 flex flex-col overflow-hidden"
        >
          <div className="border-b border-app-border"> {/* Use themed border */}
            <TabsList className="h-8 px-4 bg-transparent justify-start">
              {displayableTabs.map((tabKey, index) => {
                // Get shortcut based on index (Alt+1, Alt+2, etc.)
                const shortcutNumber = index + 1;
                const shortcut = `Alt+${shortcutNumber}`;

                return (
                  <TabsTrigger
                    key={tabKey}
                    value={tabKey.toLowerCase()}
                    // Apply themed text and active border color
                    className="h-7 px-3 text-sm rounded-none text-[var(--app-main-text)] opacity-70 data-[state=active]:opacity-100 data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-[var(--app-highlight)] data-[state=active]:font-medium"
                    title={`${tabKey} (${shortcut})`}
                  >
                    {tabKey}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {/* Tab Content - All content components are always rendered, Tabs component handles visibility */}
          <div className="flex-1 overflow-hidden"> {/* This div will inherit bg from app-main-content */}
            {ALL_CHANNEL_VIEWS_ORDERED.includes('Messages') && (
              <TabsContent value="messages" className="h-full m-0 p-0 data-[state=active]:flex data-[state=inactive]:hidden">
                <ResizablePanelGroup direction="vertical" className="h-full">
                  <ResizablePanel defaultSize={75} minSize={30}>
                    <MessageList /> {/* MessageList items will need their own theming for text/bg */}
                  </ResizablePanel>
                  <ResizableHandle className="bg-app-border hover:bg-app-highlight transition-colors" />
                  <ResizablePanel defaultSize={25} minSize={15} maxSize={70}>
                    <div className="px-6 py-3 h-full"> {/* Removed border-t since ResizableHandle provides the separator */}
                      <MessageInput isInResizablePanel={true} /> {/* MessageInput will need its own theming */}
                    </div>
                  </ResizablePanel>
                </ResizablePanelGroup>
              </TabsContent>
            )}

            {ALL_CHANNEL_VIEWS_ORDERED.includes('Topics') && (
              <TabsContent value="topics" className="h-full m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ChannelTopics channel={currentChannel} />
              </TabsContent>
            )}

            {ALL_CHANNEL_VIEWS_ORDERED.includes('Files') && (
              <TabsContent value="files" className="h-full m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ChannelFiles channel={currentChannel} />
              </TabsContent>
            )}

            {ALL_CHANNEL_VIEWS_ORDERED.includes('Members') && (
              <TabsContent value="members" className="h-full m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ChannelMembers channel={currentChannel} />
              </TabsContent>
            )}

            {ALL_CHANNEL_VIEWS_ORDERED.includes('Note') && (
              <TabsContent value="note" className="h-full m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ChannelNote channel={currentChannel} />
              </TabsContent>
            )}
          </div>
        </Tabs>
      )}

      {/* Direct Message View (No Tabs) */}
      {currentDirectMessage && (
        <div className="h-full">
          <ResizablePanelGroup direction="vertical" className="h-full">
            <ResizablePanel defaultSize={75} minSize={30}>
              <MessageList />
            </ResizablePanel>
            <ResizableHandle withHandle className="bg-app-border hover:bg-app-highlight transition-colors" />
            <ResizablePanel defaultSize={25} minSize={15} maxSize={70}>
              <div className="px-6 py-3 h-full"> {/* Removed border-t since ResizableHandle provides the separator */}
                <MessageInput isInResizablePanel={true} />
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      )}
    </div>
  );
};
