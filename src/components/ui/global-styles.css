/* Global styles for the app */

/* Disable text selection for sidebar */
.app-sidebar {
  user-select: none;
  background: var(--app-sidebar);
  color: var(--app-text);
  min-width: 200px; /* Minimum width */
  position: relative;
}

/* Resize handle styles */
.app-sidebar .resize-handle {
  position: absolute;
  right: -4px;
  top: 0;
  bottom: 0;
  width: 8px;
  cursor: col-resize;
  opacity: 0;
  transition: opacity 0.2s;
  background-color: transparent;
  z-index: 10;
}

.app-sidebar .resize-handle:hover,
.app-sidebar .resize-handle:active {
  opacity: 1;
}

.app-sidebar .resize-handle::after {
  content: "";
  position: absolute;
  top: 0;
  right: 3px;
  bottom: 0;
  width: 2px;
  background-color: var(--app-border);
  opacity: 0;
  transition: opacity 0.2s;
}

.app-sidebar .resize-handle:hover::after,
.app-sidebar .resize-handle:active::after {
  opacity: 1;
}

/* Styles for the sidebar header (workspace name area) */
.app-sidebar-header {
  border-bottom: 1px solid var(--app-border);
  /* Default hover, can be overridden by specific theme variables if needed */
}

.app-sidebar-header:hover {
  background-color: var(--app-hover-bg);
}

.app-sidebar-header .workspace-icon-bg {
  background-color: var(--app-highlight);
  color: var(--app-sidebar);
}

.app-sidebar-header .workspace-name {
  color: var(--app-highlight);
  font-weight: bold;
}

.app-main-content {
  background: var(--app-main-bg);
  color: var(--app-main-text);
}

/* Main header styling */
.app-main-content .main-header {
  background: var(--app-sidebar);
  border-bottom: 1px solid var(--app-border);
  color: var(--app-text);
}

.app-main-content .main-header button {
  color: var(--app-text);
}

.app-main-content .main-header button:hover {
  background-color: var(--app-hover-bg);
}

/* Global text color styles for main content area */

/* Channel name and description */
.app-main-content .channel-name {
  color: var(--app-main-text);
}

.app-main-content .channel-description {
  color: var(--app-main-text);
  opacity: 0.8;
}

/* Tab text colors */
.app-main-content .tab-text {
  color: var(--app-main-text);
  opacity: 0.8;
}

.app-main-content .tab-text[data-state="active"] {
  color: var(--app-main-text);
  opacity: 1;
}

/* Topic and file names */
.app-main-content .topic-name,
.app-main-content .file-name {
  color: var(--app-main-text);
}

.app-main-content .topic-description,
.app-main-content .file-info {
  color: var(--app-main-text);
  opacity: 0.7;
}

/* Date dividers and metadata */
.app-main-content .date-divider,
.app-main-content .metadata {
  color: var(--app-main-text);
  opacity: 0.6;
}

/* Message content */
.app-main-content .message-content {
  color: var(--app-main-text);
}

/* Section headers */
.app-main-content .section-header {
  color: var(--app-main-text);
  opacity: 0.9;
}

/* Enable text selection for chat content */
.user-select-text {
  user-select: text;
}

/* Hover effect for channels with consistent spacing */
.channel-hover:hover {
  background-color: var(--app-hover-bg); /* Use theme variable */
  transition: background-color 0.1s ease-in-out;
}

/* Theme colors */
/* :root will now primarily hold shadcn/ui compatible vars, defined in index.css */
/* Default app theme variables will be under .theme-default */

/* Light mode themes */
body.theme-default {
  /* Clean, professional light theme with neutral grays */
  --app-sidebar: #F8F9FA; /* Very light gray for calm sidebar */
  --app-border: #E5E7EB; /* Subtle border color */
  --app-text: #374151; /* Dark gray for good readability */
  --app-highlight: #64748B; /* Slightly darker blue-gray for better visibility */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #111827; /* Near black text for active items */
  --app-thread-highlight: rgba(75, 85, 99, 0.08); /* Very subtle gray highlight */
  --app-hover-bg: rgba(0, 0, 0, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #E5E7EB; /* Subtle gray selection - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(0, 0, 0, 0.03);
  --app-main-bg: #ffffff;
  --app-main-text: #1F2937;
  --primary: 220 17% 90%; /* Light grey for buttons */
  --primary-foreground: 215 14% 34%; /* Dark grey text for buttons */
}

/* Dark mode for default theme */
body.theme-default.dark {
  --app-sidebar: #1F2937; /* Dark gray sidebar */
  --app-border: #374151; /* Darker border */
  --app-text: #E5E7EB; /* Light gray text */
  --app-highlight: #9CA3AF; /* Light gray highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(156, 163, 175, 0.15); /* Subtle light highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.02); /* Subtle hover effect */
  --app-selected-item: #374151; /* Darker selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  --app-main-bg: #111827; /* Dark main background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 220 17% 25%; /* Dark grey for buttons */
  --primary-foreground: 220 17% 90%; /* Light grey text for buttons */
}

/* New signature theme - Azure Professional */
body.theme-azure-professional {
  /* Modern blue theme with excellent contrast and professional feel */
  --app-sidebar: #1E40AF; /* Rich blue sidebar */
  --app-border: #DBEAFE; /* Light blue border */
  --app-text: #FFFFFF; /* White text on blue sidebar */
  --app-highlight: #93C5FD; /* Light blue highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(147, 197, 253, 0.2); /* Blue highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover on blue */
  --app-selected-item: #2563EB; /* Bright blue selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FAFBFF; /* Very light blue-tinted white */
  --app-main-text: #1E293B; /* Dark slate text */
  --primary: 214 88% 52%; /* Blue for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Azure Professional theme */
body.theme-azure-professional.dark {
  --app-sidebar: #0F172A; /* Very dark blue-gray sidebar */
  --app-border: #1E293B; /* Dark blue-gray border */
  --app-text: #E2E8F0; /* Light blue-gray text */
  --app-highlight: #3B82F6; /* Bright blue highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F1F5F9; /* Very light text for active items */
  --app-thread-highlight: rgba(59, 130, 246, 0.2); /* Blue highlight */
  --app-hover-bg: rgba(226, 232, 240, 0.05); /* Subtle light hover */
  --app-selected-item: #1E40AF; /* Rich blue selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(226, 232, 240, 0.08);
  --app-main-bg: #020617; /* Very dark blue background */
  --app-main-text: #F1F5F9; /* Light blue-gray main text */
  --primary: 214 88% 52%; /* Blue for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Selected channel/item highlighting */
.selected-item {
  background-color: var(--app-selected-item) !important;
}

/* Thread message highlighting */
.thread-active-message {
  background-color: var(--app-thread-highlight) !important;
}

/* Project spacing in sidebar - ensure consistent height to prevent jumping */
.project-item {
  /* margin-bottom: 4px; Removed to use space-y on ul */
  /* Adding padding to the clickable div inside project-item for consistent height */
}
.project-item > div:first-child { /* Targeting the clickable project header */
  padding-top: 2px;
  padding-bottom: 2px;
}

/* Fix alignment for hover actions to prevent layout shift */
.project-hover-actions {
  width: 24px; /* Fixed width */
  height: 24px; /* Fixed height */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent shrinking */
  /* Opacity and pointer-events are handled by Tailwind in Sidebar.tsx */
}

/* Message styles */
.app-main-content .message {
  color: var(--app-main-text);
}

.app-main-content .message:hover {
  background-color: var(--app-hover-bg);
}

.app-main-content .message-sender {
  color: var(--app-main-text);
}

.app-main-content .message-timestamp {
  color: var(--app-main-text);
  opacity: 0.6;
}

.app-main-content .message-content {
  color: var(--app-main-text);
}

/* Neutral colors for reply counts and file links - less distracting than accent colors */
.app-main-content .reply-count-text {
  color: var(--app-main-text);
  opacity: 0.7;
}

.app-main-content .file-link-text {
  color: var(--app-main-text);
  opacity: 0.8;
}

.app-main-content .file-link-text:hover {
  opacity: 1;
}

/* Thread parent and quote styling */
.thread-parent-message {
  border-left: 3px solid var(--app-highlight);
  padding-left: 8px;
  background-color: var(--app-hover-bg);
}

.thread-quote {
  position: relative;
  padding: 8px 12px;
  background-color: var(--app-hover-bg);
  border-left: 2px solid var(--app-highlight); /* Reduced border width */
  margin: 8px 0;
  font-size: 0.95em;
  color: var(--app-main-text);
  border-radius: 4px;
}

/* Better styling for thread header */
.thread-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--app-border);
  background-color: #fff; /* Consider theme variable */
}
.dark .thread-header {
  background-color: var(--app-sidebar); /* Match sidebar for dark themes often */
}

/* User section at bottom of sidebar */
.user-section {
  background-color: var(--app-user-section);
}

.user-section:hover {
  /* Fallback to --app-hover-bg if --app-user-section-hover is not defined by the theme */
  background-color: var(--app-user-section-hover, var(--app-hover-bg));
}

/* Theme classes */
body.theme-dark {
  --app-sidebar: #1a1d21;
  --app-border: #383838;
  --app-text: #d1d2d3;
  --app-highlight: #1164a3; /* Keeping consistent unless theme demands change */
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(255, 245, 204, 0.2);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #1164a3; /* Now using original active color */
  --app-user-section: transparent; /* Match sidebar */
  --app-user-section-hover: rgba(255, 255, 255, 0.05);
  /* Assuming a dark theme might want a dark main background too */
  --app-main-bg: #121212; /* Example dark main bg */
  --app-main-text: #e0e0e0; /* Example light text for dark bg */
  --primary: 207 82% 35%; /* From --app-active */
  --primary-foreground: 0 0% 100%; /* White */
}

body.theme-royal-purple {
  /* Rich purple theme with excellent contrast */
  --app-sidebar: #4a154b; /* Deep purple sidebar */
  --app-border: #E8D5E9; /* Light purple-tinted border */
  --app-text: #FFFFFF; /* White text on purple sidebar */
  --app-highlight: #C084FC; /* Bright purple highlight for better visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(192, 132, 252, 0.15); /* Purple highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover on purple */
  --app-selected-item: #7C3AED; /* Bright purple selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FEFCFE; /* Very light purple-tinted white */
  --app-main-text: #1F2937; /* Dark gray text for better readability */
  --primary: 262 83% 58%; /* Purple for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Royal Purple theme */
body.theme-royal-purple.dark {
  --app-sidebar: #2D0A30; /* Very dark purple sidebar */
  --app-border: #4a154b; /* Dark purple border */
  --app-text: #E8D5E9; /* Light purple-tinted text */
  --app-highlight: #A855F7; /* Bright purple highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(168, 85, 247, 0.2); /* Purple highlight */
  --app-hover-bg: rgba(232, 213, 233, 0.05); /* Subtle light hover */
  --app-selected-item: #7C3AED; /* Rich purple selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(232, 213, 233, 0.08);
  --app-main-bg: #1A0B1D; /* Very dark purple background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 262 83% 58%; /* Purple for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-forest-green { /* Was theme-green */
  --app-sidebar: #0e3d2d; /* Deep forest green sidebar */
  --app-border: #D1FAE5; /* Light green-tinted border */
  --app-text: #FFFFFF; /* White text on dark green sidebar */
  --app-main-bg: #FEFFFE; /* Very light green-tinted white */
  --app-main-text: #1F2937; /* Dark gray text for better readability */
  --app-highlight: #34D399; /* Bright emerald highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(52, 211, 153, 0.15); /* Green highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover on green */
  --app-selected-item: #10B981; /* Bright emerald selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --primary: 158 64% 52%; /* Green for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Forest Green theme */
body.theme-forest-green.dark {
  --app-sidebar: #0A2E23; /* Very dark green sidebar */
  --app-border: #0e3d2d; /* Dark green border */
  --app-text: #D1FAE5; /* Light green-tinted text */
  --app-main-bg: #0F1B14; /* Very dark green background */
  --app-main-text: #F3F4F6; /* Light main text */
  --app-highlight: #10B981; /* Bright green highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(16, 185, 129, 0.2); /* Green highlight */
  --app-hover-bg: rgba(209, 250, 229, 0.05); /* Subtle light hover */
  --app-selected-item: #059669; /* Rich green selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(209, 250, 229, 0.08);
  --primary: 158 64% 52%; /* Green for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-sunset-orange { /* Was theme-orange */
  --app-sidebar: #e05d11; /* Vibrant orange sidebar */
  --app-border: #FED7AA; /* Light orange-tinted border */
  --app-text: #FFFFFF; /* White text on orange sidebar */
  --app-main-bg: #FFFBF5; /* Very light orange-tinted white */
  --app-main-text: #1F2937; /* Dark gray text for better readability */
  --app-highlight: #FB923C; /* Bright orange highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(251, 146, 60, 0.15); /* Orange highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover on orange */
  --app-selected-item: #EA580C; /* Bright orange selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --primary: 25 95% 53%; /* Orange for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Sunset Orange theme */
body.theme-sunset-orange.dark {
  --app-sidebar: #7C2D12; /* Very dark orange sidebar */
  --app-border: #e05d11; /* Dark orange border */
  --app-text: #FED7AA; /* Light orange-tinted text */
  --app-main-bg: #1C0A05; /* Very dark orange background */
  --app-main-text: #F3F4F6; /* Light main text */
  --app-highlight: #FB923C; /* Bright orange highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(251, 146, 60, 0.2); /* Orange highlight */
  --app-hover-bg: rgba(254, 215, 170, 0.05); /* Subtle light hover */
  --app-selected-item: #EA580C; /* Rich orange selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(254, 215, 170, 0.08);
  --primary: 25 95% 53%; /* Orange for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-ocean-teal { /* Was theme-jade */
  --app-sidebar: #0e6e5c; /* Deep teal sidebar */
  --app-border: #A7F3D0; /* Light teal-tinted border */
  --app-text: #FFFFFF; /* White text on teal sidebar */
  --app-main-bg: #F0FDFA; /* Very light teal-tinted white */
  --app-main-text: #1F2937; /* Dark gray text for better readability */
  --app-highlight: #14B8A6; /* Bright teal highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(20, 184, 166, 0.15); /* Teal highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover on teal */
  --app-selected-item: #0D9488; /* Bright teal selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --primary: 172 66% 50%; /* Teal for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Ocean Teal theme */
body.theme-ocean-teal.dark {
  --app-sidebar: #134E4A; /* Very dark teal sidebar */
  --app-border: #0e6e5c; /* Dark teal border */
  --app-text: #A7F3D0; /* Light teal-tinted text */
  --app-main-bg: #042F2E; /* Very dark teal background */
  --app-main-text: #F3F4F6; /* Light main text */
  --app-highlight: #14B8A6; /* Bright teal highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(20, 184, 166, 0.2); /* Teal highlight */
  --app-hover-bg: rgba(167, 243, 208, 0.05); /* Subtle light hover */
  --app-selected-item: #0D9488; /* Rich teal selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(167, 243, 208, 0.08);
  --primary: 172 66% 50%; /* Teal for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-midnight-blue { /* Corresponds to 'midnight-blue' in ThemeSelectionGrid */
  --app-sidebar: #1d2252;
  --app-border: #2a316e;
  --app-text: #ffffff; /* General text for this theme */
  --app-highlight: #a3a6ff; /* Brighter highlight */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(220, 225, 255, 0.3);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #2a3178; /* Better selection contrast - Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #10132b;
  --app-main-text: #ffffff; /* Pure white text for maximum contrast */
  --primary: 240 64% 22%;
  --primary-foreground: 0 0% 100%;
}

body.theme-graphite { /* Added based on ThemeSelectionGrid */
  --app-sidebar: #383838;
  --app-border: #505050; /* Guessed border */
  --app-text: #e0e0e0; /* General text */
  --app-highlight: #ffffff; /* Brighter highlight - Changed to white */
  --app-active: var(--app-selected-item); /* Active state, now matches selected item */
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(200, 200, 200, 0.2);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: #4a4a4a; /* Now using original active color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #2a2a2a; /* From ThemeSelectionGrid */
  --app-main-text: #ffffff; /* Pure white text for maximum contrast */
  --primary: 0 0% 22%;
  --primary-foreground: 0 0% 100%;
}


/* Gradient themes - Refined implementations */
body.theme-gradient-aqua-dream {
  --app-sidebar: linear-gradient(165deg,
    rgba(56, 142, 160, 0.98),
    rgba(79, 172, 168, 0.99));
  --app-border: rgba(79, 172, 168, 0.4);
  --app-text: #FFFFFF; /* White text for better contrast on darker gradient */
  --app-highlight: #A7F3D0; /* Light teal highlight for visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(8, 145, 178, 0.15); /* Refined highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover */
  --app-selected-item: #0891B2; /* Consistent selection color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 188 85% 37%; /* Cyan for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Aqua Dream theme */
body.theme-gradient-aqua-dream.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(21, 94, 117, 0.98),
    rgba(30, 58, 138, 0.99));
  --app-border: rgba(30, 58, 138, 0.4);
  --app-text: #E0F2FE; /* Light cyan-tinted text */
  --app-highlight: #22D3EE; /* Bright cyan highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(34, 211, 238, 0.2); /* Cyan highlight */
  --app-hover-bg: rgba(224, 242, 254, 0.05); /* Subtle light hover */
  --app-selected-item: #0891B2; /* Rich cyan selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(224, 242, 254, 0.08);
  --app-main-bg: #0C1821; /* Very dark blue background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 188 85% 37%; /* Cyan for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-gradient-sky-blue {
  --app-sidebar: linear-gradient(165deg,
    rgba(76, 159, 213, 0.98),
    rgba(118, 205, 234, 0.99));
  --app-border: rgba(118, 205, 234, 0.4);
  --app-text: #FFFFFF; /* White text on blue gradient */
  --app-highlight: #DBEAFE; /* Light blue highlight for visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(59, 130, 246, 0.15); /* Blue highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover */
  --app-selected-item: #2563EB; /* Bright blue selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 214 88% 52%; /* Blue for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Sky Blue theme */
body.theme-gradient-sky-blue.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(30, 64, 175, 0.98),
    rgba(59, 130, 246, 0.99));
  --app-border: rgba(59, 130, 246, 0.4);
  --app-text: #DBEAFE; /* Light blue-tinted text */
  --app-highlight: #60A5FA; /* Bright blue highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(96, 165, 250, 0.2); /* Blue highlight */
  --app-hover-bg: rgba(219, 234, 254, 0.05); /* Subtle light hover */
  --app-selected-item: #2563EB; /* Rich blue selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(219, 234, 254, 0.08);
  --app-main-bg: #0F172A; /* Very dark blue background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 214 88% 52%; /* Blue for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-gradient-deep-ocean {
  --app-sidebar: linear-gradient(165deg,
    rgba(29, 43, 83, 0.98),
    rgba(48, 64, 128, 0.99));
  --app-border: rgba(126, 156, 224, 0.2);
  --app-text: #ffffff;
  --app-highlight: #a3b8ff;
  --app-active: var(--app-selected-item);
  --app-active-text: #ffffff;
  --app-thread-highlight: rgba(126, 156, 224, 0.15);
  --app-hover-bg: rgba(255, 255, 255, 0.01); /* Greatly reduced hover effect */
  --app-selected-item: rgba(126, 156, 224, 0.95); /* Now using original active color */
  --app-user-section: rgba(29, 43, 83, 0.95);
  --app-user-section-hover: rgba(255, 255, 255, 0.08);
  --app-main-bg: #1a1f36;
  --app-main-text: #e6e9f0;
  --primary: 230 60% 56%;
  --primary-foreground: 0 0% 100%;
  color-scheme: dark;
}

/* Additional dark mode specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .app-main-content {
  background-color: var(--app-main-bg);
  color: var(--app-main-text);
}

body.theme-gradient-deep-ocean .app-main-content .tab-text[data-state="active"] {
  color: #ffffff;
  opacity: 1;
}

/* File view specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .app-main-content [role="tabpanel"] {
  background-color: var(--app-main-bg);
  color: var(--app-main-text);
}

/* Thread panel specific styles for Deep Ocean */
body.theme-gradient-deep-ocean .thread-header {
  background-color: #1e2440;
  border-color: rgba(126, 156, 224, 0.2);
}

body.theme-gradient-deep-ocean .thread-parent-message {
  background-color: rgba(255, 255, 255, 0.03);
  border-left-color: var(--app-highlight);
}

/* Additional UI elements for dark mode */
body.theme-gradient-deep-ocean .app-main-content input,
body.theme-gradient-deep-ocean .app-main-content textarea {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--app-main-text);
}

body.theme-gradient-deep-ocean .app-main-content input::placeholder,
body.theme-gradient-deep-ocean .app-main-content textarea::placeholder {
  color: rgba(230, 233, 240, 0.5);
}

body.theme-gradient-coral-sunrise {
  --app-sidebar: linear-gradient(165deg,
    rgba(245, 181, 207, 0.98),
    rgba(255, 170, 167, 0.99));
  --app-border: rgba(255, 170, 167, 0.4);
  --app-text: #1F2937; /* Dark text for better contrast */
  --app-highlight: #EC4899; /* Bright pink highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #1F2937; /* Dark text for active items */
  --app-thread-highlight: rgba(236, 72, 153, 0.15); /* Pink highlight */
  --app-hover-bg: rgba(0, 0, 0, 0.03); /* Subtle dark hover */
  --app-selected-item: #EC4899; /* Consistent selection color */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(0, 0, 0, 0.05);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 330 81% 60%; /* Pink for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Coral Sunrise theme */
body.theme-gradient-coral-sunrise.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(131, 24, 67, 0.98),
    rgba(159, 18, 57, 0.99));
  --app-border: rgba(159, 18, 57, 0.4);
  --app-text: #FCE7F3; /* Light pink-tinted text */
  --app-highlight: #F472B6; /* Bright pink highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(244, 114, 182, 0.2); /* Pink highlight */
  --app-hover-bg: rgba(252, 231, 243, 0.05); /* Subtle light hover */
  --app-selected-item: #EC4899; /* Rich pink selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(252, 231, 243, 0.08);
  --app-main-bg: #1F0A13; /* Very dark pink background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 330 81% 60%; /* Pink for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-gradient-meadow-green {
  --app-sidebar: linear-gradient(165deg,
    rgba(93, 123, 66, 0.98),
    rgba(138, 154, 91, 0.99));
  --app-border: rgba(138, 154, 91, 0.4);
  --app-text: #FFFFFF; /* White text on green gradient */
  --app-highlight: #D1FAE5; /* Light green highlight for visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(34, 197, 94, 0.15); /* Green highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover */
  --app-selected-item: #22C55E; /* Bright green selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 142 71% 45%; /* Green for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Meadow Green theme */
body.theme-gradient-meadow-green.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(20, 83, 45, 0.98),
    rgba(22, 101, 52, 0.99));
  --app-border: rgba(22, 101, 52, 0.4);
  --app-text: #D1FAE5; /* Light green-tinted text */
  --app-highlight: #4ADE80; /* Bright green highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(74, 222, 128, 0.2); /* Green highlight */
  --app-hover-bg: rgba(209, 250, 229, 0.05); /* Subtle light hover */
  --app-selected-item: #22C55E; /* Rich green selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(209, 250, 229, 0.08);
  --app-main-bg: #0A1F0F; /* Very dark green background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 142 71% 45%; /* Green for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-gradient-purple-haze {
  --app-sidebar: linear-gradient(165deg,
    rgba(139, 69, 219, 0.98),
    rgba(168, 85, 247, 0.99));
  --app-border: rgba(168, 85, 247, 0.4);
  --app-text: #FFFFFF; /* White text on purple gradient */
  --app-highlight: #E9D5FF; /* Light purple highlight for visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(147, 51, 234, 0.15); /* Purple highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover */
  --app-selected-item: #9333EA; /* Bright purple selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 262 83% 58%; /* Purple for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Purple Haze theme */
body.theme-gradient-purple-haze.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(88, 28, 135, 0.98),
    rgba(107, 33, 168, 0.99));
  --app-border: rgba(107, 33, 168, 0.4);
  --app-text: #E9D5FF; /* Light purple-tinted text */
  --app-highlight: #C084FC; /* Bright purple highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(192, 132, 252, 0.2); /* Purple highlight */
  --app-hover-bg: rgba(233, 213, 255, 0.05); /* Subtle light hover */
  --app-selected-item: #9333EA; /* Rich purple selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(233, 213, 255, 0.08);
  --app-main-bg: #1A0B2E; /* Very dark purple background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 262 83% 58%; /* Purple for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

body.theme-gradient-warm-glow {
  --app-sidebar: linear-gradient(165deg,
    rgba(251, 113, 133, 0.98),
    rgba(249, 168, 212, 0.99));
  --app-border: rgba(249, 168, 212, 0.4);
  --app-text: #FFFFFF; /* White text on pink gradient */
  --app-highlight: #FCE7F3; /* Light pink highlight for visibility */
  --app-active: var(--app-selected-item);
  --app-active-text: #FFFFFF; /* White text for active items */
  --app-thread-highlight: rgba(236, 72, 153, 0.15); /* Pink highlight */
  --app-hover-bg: rgba(255, 255, 255, 0.08); /* Subtle white hover */
  --app-selected-item: #EC4899; /* Bright pink selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(255, 255, 255, 0.1);
  --app-main-bg: #FFFFFF; /* Clean white background */
  --app-main-text: #1F2937; /* Dark text for readability */
  --primary: 330 81% 60%; /* Pink for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Dark mode for Warm Glow theme */
body.theme-gradient-warm-glow.dark {
  --app-sidebar: linear-gradient(165deg,
    rgba(159, 18, 57, 0.98),
    rgba(190, 24, 93, 0.99));
  --app-border: rgba(190, 24, 93, 0.4);
  --app-text: #FCE7F3; /* Light pink-tinted text */
  --app-highlight: #F472B6; /* Bright pink highlight */
  --app-active: var(--app-selected-item);
  --app-active-text: #F9FAFB; /* Very light text for active items */
  --app-thread-highlight: rgba(244, 114, 182, 0.2); /* Pink highlight */
  --app-hover-bg: rgba(252, 231, 243, 0.05); /* Subtle light hover */
  --app-selected-item: #EC4899; /* Rich pink selection */
  --app-user-section: transparent;
  --app-user-section-hover: rgba(252, 231, 243, 0.08);
  --app-main-bg: #1F0A13; /* Very dark pink background */
  --app-main-text: #F3F4F6; /* Light main text */
  --primary: 330 81% 60%; /* Pink for buttons */
  --primary-foreground: 0 0% 100%; /* White text for buttons */
}

/* Styles for SimpleMDE to ensure proper height and scrolling */
/* The .EasyMDEContainer is the root div created by SimpleMDE, which gets the className from the component prop */
/* It's already set to flex flex-col flex-grow min-h-0 via ChannelNote.tsx */

.editor-toolbar {
  flex-shrink: 0; /* Prevent toolbar from shrinking */
  position: relative; /* Keep it in flow, ensure z-index works if needed for dropdowns */
  z-index: 10; /* Ensure toolbar dropdowns are above CodeMirror */
}

/* This is the direct child of .EasyMDEContainer that wraps CodeMirror itself */
.CodeMirror-wrap {
  flex-grow: 1; /* Take remaining space from .EasyMDEContainer (which is flex-col) */
  display: flex; /* Make it a flex container for .CodeMirror */
  flex-direction: column; /* Stack .CodeMirror vertically */
  min-height: 0; /* Crucial for nested flex scrolling */
  position: relative; /* For CodeMirror's absolute positioned elements */
}

.CodeMirror {
  flex-grow: 1; /* Grow to fill .CodeMirror-wrap */
  height: 100% !important; /* Fill its parent */
  min-height: 0; /* Allow it to shrink and enable internal scrolling */
  position: relative; /* Ensure its children are positioned correctly */
}

.CodeMirror-scroll {
  overflow-y: auto !important; /* Ensure this is the scrollable part */
  height: 100%; /* Fill the .CodeMirror parent */
  min-height: 0; /* Allow shrinking */
  position: relative; /* For internal elements like cursors */
}

/* Optional: Ensure the gutter doesn't cause issues, though usually handled by CodeMirror */
.CodeMirror-gutters {
  height: 100% !important;
}

/* Ensure :active state for sidebar items uses the intended selection color */
.project-item > div:first-child:active,
.channel-hover:active {
  background-color: var(--app-selected-item) !important;
}
