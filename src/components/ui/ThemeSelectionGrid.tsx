import React from 'react';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { cn } from "@/lib/utils";
import { Check } from 'lucide-react';

interface ThemeSelectionGridProps {
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
}

interface ThemePreviewProps {
  id: string;
  value: string;
  label: string;
  sidebarBg?: string;
  sidebarGradient?: string;
  sidebarText: string;
  mainBg?: string;
  mainGradient?: string;
  mainText?: string;
  selectedLabelBg?: string;
  selectedLabelText?: string;
}

const ThemePreview: React.FC<ThemePreviewProps & { selectedTheme: string; onThemeChange: (theme: string) => void; }> = ({
  id,
  value,
  label,
  sidebarBg,
  sidebarGradient,
  sidebarText,
  mainBg,
  mainGradient,
  mainText = '#1d1c1d',
  selectedLabelBg, // New prop
  selectedLabelText, // New prop
  selectedTheme,
  onThemeChange
}) => {
  let effectiveMainText = mainText;
  if (mainBg && mainText === '#1d1c1d') {
    const isMainBgDark = mainBg.startsWith('#') && parseInt(mainBg.substring(1, 3), 16) * 0.299 + parseInt(mainBg.substring(3, 5), 16) * 0.587 + parseInt(mainBg.substring(5, 7), 16) * 0.114 < 128;
    effectiveMainText = isMainBgDark ? '#f0f0f0' : mainText;
  }

  const sidebarStyle: React.CSSProperties = {
    color: sidebarText,
  };
  if (sidebarGradient) {
    sidebarStyle.backgroundImage = sidebarGradient;
  } else if (sidebarBg) {
    sidebarStyle.backgroundColor = sidebarBg;
  }

  const mainStyle: React.CSSProperties = {
    color: effectiveMainText,
  };
  if (mainBg) { // Always apply mainBg if it's defined
    mainStyle.backgroundColor = mainBg;
  }
  if (mainGradient) { // Apply mainGradient, will layer over backgroundColor if transparent
    mainStyle.backgroundImage = mainGradient;
  }

  const isSelected = selectedTheme === value;

  return (
    <button
      onClick={() => onThemeChange(value)}
      className={cn(
        "group relative w-full text-left transition-all duration-200",
        "rounded-lg overflow-hidden border-2",
        isSelected
          ? "border-primary shadow-md scale-[0.98]"
          : "border-transparent hover:border-muted hover:scale-[1.02]",
        "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring"
      )}
    >
      <div className="relative h-20">
        <div className="absolute inset-0 flex">
          <div
            className="w-1/3 h-full flex flex-col items-center justify-center p-1"
            style={sidebarStyle}
          >
            <span className="text-xs font-medium opacity-80">Sidebar</span>
            <div className="w-full h-1 bg-current opacity-30 my-0.5 rounded-sm"></div>
            <div className="w-4/5 h-1 bg-current opacity-20 rounded-sm"></div>
          </div>
          <div
            className="w-2/3 h-full flex flex-col items-start justify-start p-2"
            style={mainStyle}
          >
            <span className="text-xs font-medium opacity-80">Main Content</span>
            <div className="w-full h-1.5 bg-current opacity-10 my-1 rounded-sm"></div>
            <div className="w-3/4 h-1.5 bg-current opacity-10 rounded-sm"></div>
          </div>
        </div>
      </div>
      <div
        className={cn(
          "px-3 py-2 text-sm font-medium flex items-center justify-between",
          !isSelected && "bg-muted/50 hover:bg-muted/80" // Apply non-selected style only
        )}
        style={isSelected ? { backgroundColor: selectedLabelBg || sidebarBg, color: selectedLabelText || sidebarText } : {}}
      >
        <span>{label}</span>
        {isSelected && (
          <Check
            className="w-4 h-4 animate-in fade-in duration-200"
            style={{ color: selectedLabelText || sidebarText }}
          />
        )}
      </div>
    </button>
  );
};

export const ThemeSelectionGrid: React.FC<ThemeSelectionGridProps> = ({
  selectedTheme,
  onThemeChange,
}) => {
  const singleColorThemes: ThemePreviewProps[] = [
    { id: 'theme-azure-professional', value: 'azure-professional', label: 'Azure Professional', sidebarBg: '#1E40AF', sidebarText: '#FFFFFF', mainBg: '#FAFBFF', mainText: '#1E293B', selectedLabelBg: '#1E40AF', selectedLabelText: '#FFFFFF' },
    { id: 'theme-royal-purple', value: 'royal-purple', label: 'Royal Purple', sidebarBg: '#4a154b', sidebarText: '#FFFFFF', mainBg: '#FEFCFE', mainText: '#1F2937', selectedLabelBg: '#4a154b', selectedLabelText: '#FFFFFF' },
    { id: 'theme-forest-green', value: 'forest-green', label: 'Forest Green', sidebarBg: '#0e3d2d', sidebarText: '#FFFFFF', mainBg: '#FEFFFE', mainText: '#1F2937', selectedLabelBg: '#0e3d2d', selectedLabelText: '#FFFFFF' },
    { id: 'theme-sunset-orange', value: 'sunset-orange', label: 'Sunset Orange', sidebarBg: '#e05d11', sidebarText: '#FFFFFF', mainBg: '#FFFBF5', mainText: '#1F2937', selectedLabelBg: '#e05d11', selectedLabelText: '#FFFFFF' },
    { id: 'theme-ocean-teal', value: 'ocean-teal', label: 'Ocean Teal', sidebarBg: '#0e6e5c', sidebarText: '#FFFFFF', mainBg: '#F0FDFA', mainText: '#1F2937', selectedLabelBg: '#0e6e5c', selectedLabelText: '#FFFFFF' },
    { id: 'theme-default-light', value: 'default', label: 'Default Light', sidebarBg: '#F8F9FA', sidebarText: '#374151', mainBg: '#ffffff', mainText: '#1F2937', selectedLabelBg: '#E5E7EB', selectedLabelText: '#111827' },
  ];

  const gradientThemes: ThemePreviewProps[] = [
    {
      id: 'theme-gradient-aqua-dream',
      value: 'gradient-aqua-dream',
      label: 'Aqua Dream',
      sidebarGradient: 'linear-gradient(165deg, rgba(105, 210, 231, 0.98), rgba(167, 219, 216, 0.99))',
      sidebarText: '#1F2937',
      mainBg: '#ffffff',
      mainText: '#1F2937',
      selectedLabelBg: 'rgb(8, 145, 178)',
      selectedLabelText: '#ffffff'
    },
    {
      id: 'theme-gradient-sky-blue',
      value: 'gradient-sky-blue',
      label: 'Sky Blue',
      sidebarGradient: 'linear-gradient(165deg, rgba(76, 159, 213, 0.98), rgba(118, 205, 234, 0.99))',
      sidebarText: '#ffffff',
      mainBg: '#ffffff',
      mainText: '#1F2937',
      selectedLabelBg: 'rgb(37, 99, 235)',
      selectedLabelText: '#ffffff'
    },
    {
      id: 'theme-gradient-coral-sunrise',
      value: 'gradient-coral-sunrise',
      label: 'Coral Sunrise',
      sidebarGradient: 'linear-gradient(165deg, rgba(245, 181, 207, 0.98), rgba(255, 170, 167, 0.99))',
      sidebarText: '#1F2937',
      mainBg: '#ffffff',
      mainText: '#1F2937',
      selectedLabelBg: 'rgb(236, 72, 153)',
      selectedLabelText: '#ffffff'
    },
    {
      id: 'theme-gradient-meadow-green',
      value: 'gradient-meadow-green',
      label: 'Meadow Green',
      sidebarGradient: 'linear-gradient(165deg, rgba(93, 123, 66, 0.98), rgba(138, 154, 91, 0.99))',
      sidebarText: '#ffffff',
      mainBg: '#ffffff',
      mainText: '#1F2937',
      selectedLabelBg: 'rgb(34, 197, 94)',
      selectedLabelText: '#ffffff'
    }
  ];

  return (
    <Tabs defaultValue="single-color-themes" className="w-full">
      <TabsList className="grid w-full grid-cols-3 h-12 mb-6 bg-muted/50">
        <TabsTrigger
          value="single-color-themes"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Single Colors
        </TabsTrigger>
        <TabsTrigger
          value="gradient-themes"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Gradients
        </TabsTrigger>
        <TabsTrigger
          value="text-size"
          className="data-[state=active]:bg-background data-[state=active]:shadow-sm data-[state=active]:text-foreground/90"
        >
          Text Size
        </TabsTrigger>
      </TabsList>

      <TabsContent value="single-color-themes" className="space-y-4 focus-visible:ring-0">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {singleColorThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              {...theme}
              selectedTheme={selectedTheme}
              onThemeChange={onThemeChange}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="gradient-themes" className="space-y-4 focus-visible:ring-0">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {gradientThemes.map((theme) => (
            <ThemePreview
              key={theme.id}
              {...theme}
              selectedTheme={selectedTheme}
              onThemeChange={onThemeChange}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="text-size" className="focus-visible:ring-0">
        <div className="pt-2 space-y-6">
          <div>
            <Label className="text-base font-medium block mb-4">Adjust Text Size</Label>
            <div className="grid grid-cols-5 gap-3">
              <button
                className={cn(
                  "py-2 px-4 text-xs border rounded-md",
                  "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "transition-colors duration-200"
                )}
              >
                X-Small
              </button>
              <button
                className={cn(
                  "py-2 px-4 text-sm border rounded-md",
                  "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "transition-colors duration-200"
                )}
              >
                Small
              </button>
              <button
                className={cn(
                  "py-2 px-4 text-sm border rounded-md",
                  "bg-primary text-primary-foreground",
                  "focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "transition-colors duration-200"
                )}
              >
                Medium
              </button>
              <button
                className={cn(
                  "py-2 px-4 text-sm border rounded-md",
                  "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "transition-colors duration-200"
                )}
              >
                Large
              </button>
              <button
                className={cn(
                  "py-2 px-4 text-base border rounded-md",
                  "hover:bg-muted/80 focus:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  "transition-colors duration-200"
                )}
              >
                X-Large
              </button>
            </div>
          </div>

          <div className="pt-4">
            <Label className="text-base font-medium block mb-3">Preview</Label>
            <div className="p-6 bg-muted/30 rounded-lg space-y-3">
              <p className="text-base font-medium text-foreground/90">Sample Text Preview</p>
              <p className="text-sm text-muted-foreground leading-relaxed">
                This preview shows how your text will appear throughout the application.
                The size you select will be applied to most text elements, helping you
                find the perfect balance between readability and screen space.
              </p>
            </div>
          </div>

          <p className="text-xs text-muted-foreground mt-4">
            Note: Text size adjustments are not fully implemented in this preview.
          </p>
        </div>
      </TabsContent>
    </Tabs>
  );
};
