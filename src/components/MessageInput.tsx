import React, { useState, useRef, useEffect, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { useApp } from '@/lib/app-context';
import { Send, Plus, Smile, AtSign, Edit3 } from 'lucide-react';
import SimpleMDEEditor from 'react-simplemde-editor';
import 'easymde/dist/easymde.min.css';
import type { Options } from 'easymde';
import EasyMDE from 'easymde';
import { LocalAttachment } from '@/lib/types';
import { v4 as uuidv4 } from 'uuid';
import { AttachmentPill } from './AttachmentPill';
import { EmojiPicker } from './EmojiPicker';
import { Button } from './ui/button';

interface MessageInputProps {
  threadId?: string;
  placeholder?: string;
  autoFocus?: boolean;
  topicId?: string;
  disabled?: boolean;
  isInResizablePanel?: boolean; // New prop to indicate if it's in a resizable panel
}

export const MessageInput = ({
  threadId,
  placeholder = "Type a message...",
  autoFocus = false,
  topicId,
  disabled = false,
  isInResizablePanel = false
}: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const [pendingAttachments, setPendingAttachments] = useState<LocalAttachment[]>([]);
  const [showMarkdownToolbar, setShowMarkdownToolbar] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isEditorFocused, setIsEditorFocused] = useState(false);
  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const { sendMessage, currentChannel, currentDirectMessage, workspaceSettings } = useApp();
  const editorInstanceRef = useRef<EasyMDE | null>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateImageName = (file: File): string => {
    // If the file has a meaningful name (not generic), use it
    if (file.name && file.name !== 'image.png' && file.name !== 'image' && !file.name.startsWith('blob:')) {
      return file.name;
    }

    // Generate a better name for pasted/generic images
    const now = new Date();
    const timeStr = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/:/g, '-');
    const dateStr = now.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
    const extension = file.type.split('/')[1] || 'png';
    return `Screenshot ${dateStr} at ${timeStr}.${extension}`;
  };

  const addFileAsAttachment = async (file: File) => {
    const localId = uuidv4();
    const fileName = file.type.startsWith('image/') ? generateImageName(file) : file.name;

    const newAttachmentBase: Omit<LocalAttachment, 'dataUrl' | 'textContent'> = {
      id: localId,
      name: fileName,
      type: file.type,
      size: file.size,
      fileObject: file,
    };

    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, dataUrl: e.target?.result as string }]);
      };
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('text/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPendingAttachments(prev => [...prev, { ...newAttachmentBase, textContent: e.target?.result as string }]);
      };
      reader.readAsText(file);
    } else {
      console.warn(`File type ${file.type} not directly supported for inline content. Will be handled by upload later.`);
      setPendingAttachments(prev => [...prev, { ...newAttachmentBase }]);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setPendingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const renameAttachment = (attachmentId: string, newName: string) => {
    setPendingAttachments(prev =>
      prev.map(att =>
        att.id === attachmentId ? { ...att, name: newName } : att
      )
    );
  };

  const handleSendMessage = () => {
    if ((!message.trim() && pendingAttachments.length === 0) || disabled) return;

    if (topicId) {
      sendMessage(message, currentChannel?.id, undefined, threadId, topicId, pendingAttachments);
    } else {
      sendMessage(message, currentChannel?.id, currentDirectMessage?.id, threadId, undefined, pendingAttachments);
    }
    setMessage('');
    setPendingAttachments([]);
    editorInstanceRef.current?.codemirror.focus();
  };

  const handleEditorKeyDown = (_instance: any, event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    } else if (event.key === 'Escape') {
      event.preventDefault();
      if (isExpanded) {
        setIsExpanded(false);
      } else {
        editorInstanceRef.current?.codemirror.getInputField().blur();
      }
    }
  };

  const handlePaste = (_instance: any, event: ClipboardEvent) => {
    if (event.clipboardData) {
      for (let i = 0; i < event.clipboardData.items.length; i++) {
        const item = event.clipboardData.items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) {
            event.preventDefault();
            addFileAsAttachment(file);
          }
        }
      }
    }
  };

  const simpleMdeOptions: Options = useMemo(() => {
    return {
      autofocus: autoFocus,
      placeholder: disabled ? "Cannot send messages to an archived topic" : placeholder,
      toolbar: showMarkdownToolbar ? [
        'bold', 'italic', 'strikethrough', '|',
        'heading', 'heading-smaller', 'heading-bigger', '|',
        'code', 'quote', '|',
        'unordered-list', 'ordered-list', '|',
        'link', 'image', 'table', '|',
        'horizontal-rule', 'preview'
      ] : false,
      status: false,
      spellChecker: false,
      // Adjust height based on whether we're in a resizable panel and expanded state
      minHeight: isInResizablePanel
        ? (isExpanded ? "200px" : "80px") // Increased minimum height for better single line visibility
        : (isExpanded ? "200px" : "65px"),
      maxHeight: isInResizablePanel
        ? (isExpanded ? "calc(100% - 120px)" : "calc(100% - 120px)") // In resizable panel, use available space minus toolbar
        : (isExpanded ? "calc(100vh - 200px)" : "65px"), // Original behavior for non-resizable contexts
      autoDownloadFontAwesome: false,
      // Enable line wrapping for better multi-line experience
      lineWrapping: true,
      // Disable conflicting shortcuts to prevent interference with global app shortcuts
      shortcuts: {
        toggleBold: null,
        toggleItalic: null,
        toggleStrikethrough: null,
        toggleCodeBlock: null,
        toggleHeadingSmaller: null,
        toggleHeadingBigger: null,
        toggleHeading1: null,
        toggleHeading2: null,
        toggleHeading3: null,
        toggleUnorderedList: null,
        toggleOrderedList: null,
        cleanBlock: null,
        drawLink: null,
        drawImage: null,
        drawTable: null,
        drawHorizontalRule: null,
        undo: null,
        redo: null,
        togglePreview: null,
        toggleSideBySide: null,
        toggleFullScreen: null
      }
    };
  }, [autoFocus, placeholder, disabled, showMarkdownToolbar, isExpanded, isInResizablePanel]);

  useEffect(() => {
    if (autoFocus && editorInstanceRef.current) {
      setTimeout(() => editorInstanceRef.current?.codemirror.focus(), 0);
    }
  }, [autoFocus, currentChannel, currentDirectMessage]);

  useEffect(() => {
    const currentEditor = editorInstanceRef.current;
    if (currentEditor?.codemirror) {
        const cm = currentEditor.codemirror;
        const handleFocus = () => setIsEditorFocused(true);
        const handleBlur = () => setIsEditorFocused(false);

        cm.on('focus', handleFocus);
        cm.on('blur', handleBlur);

        if (cm.hasFocus()) {
            setIsEditorFocused(true);
        } else {
            setIsEditorFocused(false);
        }

        return () => {
            if (cm && typeof cm.off === 'function') {
                cm.off('focus', handleFocus);
                cm.off('blur', handleBlur);
            }
        };
    }
  }, [editorInstanceRef.current]); // Re-run if the editor instance changes

  useEffect(() => {
    // This effect handles editor adjustments when isExpanded state changes
    if (editorInstanceRef.current?.codemirror) {
      const cm = editorInstanceRef.current.codemirror;

      // Refresh the editor to apply new options and sizing
      cm.refresh();

      if (isExpanded) {
        cm.focus(); // Focus after expansion
      }
    }
  }, [isExpanded, editorInstanceRef.current]); // Rerun when isExpanded or editor instance changes.

  useEffect(() => {
    // Auto-resize editor based on content
    if (editorInstanceRef.current?.codemirror) {
      const cm = editorInstanceRef.current.codemirror;

      const autoResize = () => {
        const lineCount = cm.lineCount();
        const lineHeight = 20; // Approximate line height in pixels
        const padding = 20; // Top and bottom padding
        const singleLineHeight = isInResizablePanel ? 60 : 50; // Taller for resizable panel context

        if (isExpanded) {
          // In expanded mode, use larger min/max heights
          const minHeight = 200;
          const maxHeight = isInResizablePanel
            ? Math.max(300, window.innerHeight * 0.6) // In resizable panel, use more conservative max height
            : window.innerHeight - 200; // Original behavior for fixed positioning
          const contentHeight = Math.max(minHeight, Math.min(maxHeight, lineCount * lineHeight + padding));
          cm.setSize(null, contentHeight + 'px');
        } else {
          // In collapsed mode, start with single line but expand as needed
          if (lineCount <= 1 && !message.includes('\n')) {
            // Single line - use default height
            cm.setSize(null, singleLineHeight + 'px');
          } else {
            // Multi-line content - expand but with reasonable limit
            const maxHeight = isInResizablePanel ? 150 : 200; // Slightly smaller max in resizable panel
            const contentHeight = Math.max(singleLineHeight, Math.min(maxHeight, lineCount * lineHeight + padding));
            cm.setSize(null, contentHeight + 'px');
          }
        }
      };

      // Listen for content changes
      cm.on('change', autoResize);

      // Initial resize
      autoResize();

      return () => {
        if (cm && typeof cm.off === 'function') {
          cm.off('change', autoResize);
        }
      };
    }
  }, [message, isExpanded, isInResizablePanel, editorInstanceRef.current]);

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    if (disabled) return;

    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      Array.from(event.dataTransfer.files).forEach(file => {
        addFileAsAttachment(file);
      });
      event.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      Array.from(event.target.files).forEach(file => {
        addFileAsAttachment(file);
      });
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    if (editorInstanceRef.current) {
      const cm = editorInstanceRef.current.codemirror;
      const cursor = cm.getCursor();
      cm.replaceRange(emoji, cursor);
      cm.focus();
    }
  };

  const handleMentionClick = () => {
    if (editorInstanceRef.current) {
      const cm = editorInstanceRef.current.codemirror;
      const cursor = cm.getCursor();
      cm.replaceRange('@', cursor);
      cm.focus();
    }
  };

  const toggleMarkdownToolbar = () => {
    setShowMarkdownToolbar(!showMarkdownToolbar);
  };

  const toggleExpanded = () => {
    setIsExpanded(prev => !prev);
    // The useEffect hook above now handles refresh and height adjustments.
    // Focusing will be handled by the key prop causing a re-mount,
    // which should respect the `autofocus` option if applicable,
    // or by the user clicking into the editor.
  };

  return (
    <div
      ref={dropZoneRef}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      className={cn(
        "flex flex-col rounded-lg bg-[var(--app-main-bg)] shadow-sm transition-all duration-200",
        "border border-[var(--app-border)]", // Base border style
        isExpanded && !isInResizablePanel
          ? "fixed left-[260px] right-2 bottom-2 top-[152px] z-40" // Fixed positioning only when not in resizable panel
          : isInResizablePanel
          ? "relative h-full" // In resizable panel, always use full height of the panel
          : "relative", // Default relative positioning
        isEditorFocused
          ? "outline outline-2 outline-offset-[-1px] outline-[var(--app-highlight)]" // Focus style using outline
          : "outline-none" // No outline when not focused
      )}
    >
      {/* Attachments area - only show when there are attachments */}
      {pendingAttachments.length > 0 && (
        <div className="px-3 py-2 border-b border-[var(--app-border)] flex flex-wrap gap-2">
          {pendingAttachments.map(att => (
            <AttachmentPill key={att.id} attachment={att} onRemove={removeAttachment} onRename={renameAttachment} />
          ))}
        </div>
      )}

      {/* Main editor area with expand/collapse buttons */}
      <div className="relative flex-grow flex flex-col">
        <div
          className={`message-input-editor-wrapper flex-grow ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
          {...(isInResizablePanel ? { 'data-resizable-panel': 'true' } : {})}
        >
          <SimpleMDEEditor
            key={isExpanded ? 'expanded-editor' : 'collapsed-editor'} // Force re-mount on expansion change
            value={message}
            onChange={setMessage}
            options={simpleMdeOptions}
            getMdeInstance={(instance) => {
              editorInstanceRef.current = instance;
            }}
            events={{
              keydown: handleEditorKeyDown,
              paste: handlePaste,
            }}
          />
        </div>

        {/* Expand/Collapse buttons on the right edge - only show when not in resizable panel */}
        {!isInResizablePanel && (
          <div className="absolute right-2 top-2 flex flex-col gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-[var(--app-hover-bg)] opacity-60 hover:opacity-100"
              onClick={toggleExpanded}
              disabled={disabled}
              title={isExpanded ? "Collapse" : "Expand"}
            >
            {isExpanded ? (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M8 18L12 14L16 18" />
                <path d="M8 6L12 10L16 6" />
              </svg>
            ) : (
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M8 6L12 2L16 6" />
                <path d="M8 18L12 22L16 18" />
              </svg>
            )}
          </Button>
        </div>
        )}
      </div>

      {/* Bottom toolbar */}
      <div className="flex items-center px-3 py-2 border-t border-[var(--app-border)] bg-[var(--app-main-bg)]">
        <input
          type="file"
          multiple
          ref={fileInputRef}
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,text/plain,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar"
        />

        {/* Left side buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled}
            title="Attach file"
          >
            <Plus size={16} />
          </Button>

          <EmojiPicker
            onEmojiSelect={handleEmojiSelect}
            workspaceSettings={workspaceSettings}
            open={emojiPickerOpen}
            onOpenChange={setEmojiPickerOpen}
          >
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
              disabled={disabled}
              title="Add emoji"
            >
              <Smile size={16} />
            </Button>
          </EmojiPicker>

          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)]"
            onClick={handleMentionClick}
            disabled={disabled}
            title="Mention someone"
          >
            <AtSign size={16} />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 hover:bg-[var(--app-hover-bg)] ${showMarkdownToolbar ? 'bg-[var(--app-hover-bg)]' : ''}`}
            onClick={toggleMarkdownToolbar}
            disabled={disabled}
            title="Toggle markdown toolbar"
          >
            <Edit3 size={16} />
          </Button>
        </div>

        {/* Center spacer */}
        <div className="flex-grow" />

        {/* Right side buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 transition-colors ${
              (message.trim() || pendingAttachments.length > 0) && !disabled
                ? 'text-[var(--app-highlight)] hover:text-[var(--app-active)] hover:bg-[var(--app-hover-bg)]'
                : 'text-[var(--app-main-text)] opacity-30'
            } ${disabled ? 'cursor-not-allowed' : ''}`}
            onClick={handleSendMessage}
            disabled={(!message.trim() && pendingAttachments.length === 0) || disabled}
            title="Send message"
          >
            <Send size={16} />
          </Button>
        </div>
      </div>
    </div>
  );
};
