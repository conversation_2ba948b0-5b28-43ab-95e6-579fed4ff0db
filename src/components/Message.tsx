import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { MessageCircle, Info, Copy, Check, SmilePlus, Download } from 'lucide-react'; // Added Download
import { Message as MessageType, User as UserType, WorkspaceDisplayUser, ReactionSummary, File as FileType } from '@/lib/types'; // Added FileType
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { generateDisplayId, formatTimestamp } from '@/lib/utils';
import { EmojiPicker } from '@/components/EmojiPicker';
import { DefaultEmojiBar } from '@/components/DefaultEmojiBar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Helper component for copy-to-clipboard button
const CopyButton = ({ textToCopy }: { textToCopy: string }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };

  return (
    <button
      onClick={handleCopy}
      className="p-1 text-[var(--app-secondary-text)] hover:text-[var(--app-main-text)] focus:outline-none"
      aria-label={copied ? "Copied!" : "Copy ID"}
    >
      {copied ? <Check size={14} className="text-green-500" /> : <Copy size={14} />}
    </button>
  );
};

interface MessageProps {
  message: MessageType;
  showThread?: boolean;
  onClick?: () => void;
  isInThread?: boolean;
  isActive?: boolean;
  isParentMessage?: boolean;
  onAvatarClick?: (user: UserType) => void;
}

export const Message = ({
  message,
  showThread = false,
  onClick,
  isInThread = false,
  isActive = false,
  isParentMessage = false,
  onAvatarClick
}: MessageProps) => {
  const { workspace, addReaction, currentThread, setActiveThread, getCurrentUser, currentChannel, currentDirectMessage } = useApp();
  const { selectedMessageId, setSelectedMessageId } = useKeyboardShortcuts();
  const [showActionButtons, setShowActionButtons] = useState(false);
  const [showChannelInfo, setShowChannelInfo] = useState(false);
  const [emojiPickerOpen, setEmojiPickerOpen] = useState(false);
  const user = workspace?.users.find(u => u.id === message.userId) as WorkspaceDisplayUser | undefined;
  const appCurrentUser = getCurrentUser();
  const messageRef = useRef<HTMLDivElement>(null);

  const isSelected = selectedMessageId === message.id;

  let showStatusIndicator = workspace.settings?.showStatusIndicatorInMessagesDefault ?? true;
  if (appCurrentUser?.settings?.showStatusIndicatorInMessagesOverride !== null && appCurrentUser?.settings?.showStatusIndicatorInMessagesOverride !== undefined) {
    showStatusIndicator = appCurrentUser.settings.showStatusIndicatorInMessagesOverride;
  }

  // Check for thread in current context (channel or DM), not just the currently active thread
  const contextualThread = currentChannel?.threads[message.id] || currentDirectMessage?.threads[message.id];

  // Use the contextual thread if it exists, otherwise fall back to currentThread if it matches
  const thread = contextualThread || (currentThread && currentThread.parentMessageId === message.id ? currentThread : undefined);

  const replyCount = thread ? thread.messages.length - 1 : 0;
  const hasReplies = replyCount > 0;

  if (!user) {
    return null;
  }

  const handleReactionClick = (e: React.MouseEvent, emoji: string) => {
    e.stopPropagation();
    addReaction(message.id, emoji);
  };

  const handleEmojiSelect = (emoji: string) => {
    addReaction(message.id, emoji);
  };

  const handleReplyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setActiveThread(message.id);
  };

  const handleMessageClick = () => {
    if (currentThread && !isInThread) {
      setActiveThread(message.id);
    } else if (onClick) {
      onClick();
    }
  };

  const handleSelectMessage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMessageId(message.id);
  };

  useEffect(() => {
    if (isSelected && messageRef.current) {
      messageRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      messageRef.current.focus();
    }
  }, [isSelected]);

  const getReactorNames = (userIds: string[]): string => {
    if (!workspace?.users) return 'Someone';
    const names = userIds.map(uid => {
      const reactor = workspace.users.find(u => u.id === uid);
      return reactor ? reactor.name : 'Unknown User';
    });
    if (names.length > 3) {
      return `${names.slice(0, 3).join(', ')}, and ${names.length - 3} others`;
    }
    return names.join(', ');
  };

  const renderFile = (file: FileType) => {
    // In Phase 1/2, file.url might be base64 data or text content
    if (file.type.startsWith('image/') && file.url?.startsWith('data:image')) {
      return (
        <div key={file.id} className="mt-2">
          <img src={file.url} alt={file.name} className="max-w-xs max-h-64 rounded border border-gray-300 dark:border-gray-600" />
          <a href={file.url} download={file.name} className="text-xs file-link-text hover:underline ml-2">
            {file.name}
          </a>
        </div>
      );
    } else if (file.type.startsWith('text/') && file.url && !file.url.startsWith('data:')) {
      // Assuming text content is directly in file.url for Phase 1/2
      // Create a blob URL for downloading text content
      const blob = new Blob([file.url], { type: file.type });
      const blobUrl = URL.createObjectURL(blob);
      // It's important to revoke blobUrl when component unmounts or file changes, but for simplicity here, we don't.
      // In a real app, manage blob URLs carefully.
      return (
        <div key={file.id} className="mt-2 p-2 border rounded-md bg-gray-50 dark:bg-gray-700">
          <p className="text-sm font-medium">{file.name}</p>
          {/* <pre className="text-xs whitespace-pre-wrap max-h-32 overflow-auto bg-white dark:bg-gray-800 p-1 mt-1 rounded">
            {file.url.substring(0, 200)}{file.url.length > 200 ? '...' : ''}
          </pre> */}
          <a
            href={blobUrl}
            download={file.name}
            className="text-xs file-link-text hover:underline mt-1 inline-flex items-center"
            onContextMenu={(e) => e.stopPropagation()} // Allow right-click context menu on link
          >
            <Download size={12} className="mr-1" /> Download {file.name}
          </a>
        </div>
      );
    }
    // Fallback for other file types or if URL is a real URL (Phase 3)
    return (
      <div key={file.id} className="mt-2">
        <a href={file.url} target="_blank" rel="noopener noreferrer" download={file.name} className="text-xs file-link-text hover:underline inline-flex items-center">
          <Download size={12} className="mr-1" /> {file.name} ({Math.round(file.size_bytes / 1024)} KB)
        </a>
      </div>
    );
  };

  return (
    <div
      ref={messageRef}
      className={`message group flex items-start py-1 px-2 rounded ${isInThread ? 'pl-0' : ''}
        ${isActive ? 'thread-active-message' : ''}
        ${isParentMessage ? 'thread-parent-message' : ''}
        ${isSelected ? 'bg-[var(--app-hover-bg)] outline outline-2 outline-[var(--app-highlight)]' : ''}`}
      onClick={(e) => {
        handleMessageClick();
        handleSelectMessage(e);
      }}
      onMouseEnter={() => setShowActionButtons(true)}
      onMouseLeave={() => setShowActionButtons(false)}
      tabIndex={0}
      aria-selected={isSelected}
      data-message-id={message.id}
    >
      <div className="flex-shrink-0 mt-0.5 relative">
        <button
          onClick={(e) => {
            e.stopPropagation();
            if (onAvatarClick) {
              onAvatarClick(user);
            }
          }}
          className="focus:outline-none relative"
          aria-label={`View profile of ${user.name}`}
        >
          <img
            src={user.avatar}
            alt={user.name}
            className="w-9 h-9 rounded"
          />
          {showStatusIndicator && user.status && (
            <>
              {user.status === 'online' && (<div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-[var(--app-main-bg)]"></div>)}
              {user.status === 'away' && (<div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-yellow-500 rounded-full border border-[var(--app-main-bg)]"></div>)}
              {user.status === 'busy' && (<div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-red-500 rounded-full border border-[var(--app-main-bg)]"></div>)}
              {user.status === 'dnd' && (<div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-purple-500 rounded-full border border-[var(--app-main-bg)]"></div>)}
              {user.status === 'offline' && (<div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-gray-400 rounded-full border border-[var(--app-main-bg)]"></div>)}
            </>
          )}
        </button>
      </div>
      <div className="ml-2 flex-1">
        <div className="flex items-baseline">
          <span className="message-sender font-bold">{user.name}</span>
          <span className="message-timestamp ml-2 text-xs">{formatTimestamp(message.timestamp)}</span>
          {!isInThread && (
            <Popover open={showChannelInfo} onOpenChange={setShowChannelInfo}>
              <PopoverTrigger asChild>
                <button className="ml-2 opacity-60 hover:opacity-100" onClick={(e) => { e.stopPropagation(); setShowChannelInfo(true); }} aria-label="Message details">
                  <Info size={14} />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-4" align="start">
                <div className="space-y-3">
                  <h4 className="font-medium">Message Details</h4>
                  <div className="grid grid-cols-[auto,1fr,auto] items-center gap-x-2 gap-y-1 text-sm">
                    <div className="opacity-70">Sender</div><div className="truncate col-span-2">{user.name}</div>
                    <div className="opacity-70">Sent</div><div className="truncate col-span-2">{new Date(message.timestamp).toLocaleString()}</div>
                    <div className="opacity-70">Message ID</div><div className="truncate">{message.displayId || message.id}</div><CopyButton textToCopy={message.displayId || message.id} />
                    {message.channelId && (<><div className="opacity-70">Channel ID</div><div className="truncate">{generateDisplayId(message.channelId, 'c-')}</div><CopyButton textToCopy={generateDisplayId(message.channelId, 'c-')} /></>)}
                    {message.topicId && (<><div className="opacity-70">Topic ID</div><div className="truncate">{generateDisplayId(message.topicId, 't-')}</div><CopyButton textToCopy={generateDisplayId(message.topicId, 't-')} /></>)}
                    {message.threadId && (<><div className="opacity-70">Thread ID</div><div className="truncate">{generateDisplayId(message.threadId, 'th-')}</div><CopyButton textToCopy={generateDisplayId(message.threadId, 'th-')} /><div className="opacity-70">Thread</div><div className="truncate">Part of a thread</div></>)}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
        <div className="message-content mt-1 whitespace-pre-wrap user-select-text">{message.content}</div>

        {/* Render Attachments */}
        {message.files && message.files.length > 0 && (
          <div className="mt-2 space-y-2">
            {message.files.map(file => renderFile(file))}
          </div>
        )}

        {message.reactions_summary && message.reactions_summary.length > 0 && (
          <div className="mt-1.5 flex flex-wrap gap-1.5 items-center">
            <TooltipProvider delayDuration={100}>
              {message.reactions_summary.map((reaction: ReactionSummary) => {
                const currentUserReacted = reaction.user_ids_array.includes(appCurrentUser.id);
                return (
                  <Tooltip key={reaction.emoji}>
                    <TooltipTrigger asChild>
                      <button
                        onClick={(e) => handleReactionClick(e, reaction.emoji)}
                        className={`flex items-center text-xs rounded-full px-2 py-0.5 border ${currentUserReacted ? 'bg-[var(--app-active)] text-[var(--app-active-text)] border-[var(--app-active-border)]' : 'bg-[var(--app-hover-bg)] hover:bg-[var(--app-hover-bg-stronger)] border-transparent hover:border-[var(--app-secondary-border)]'} transition-colors duration-100 ease-in-out`}
                        aria-label={`React with ${reaction.emoji}`}
                      >
                        <span className="mr-1 text-sm">{reaction.emoji}</span>
                        <span className="text-xs font-medium">{reaction.count}</span>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="top" className="text-xs p-1.5">
                      <p>{getReactorNames(reaction.user_ids_array)} reacted with {reaction.emoji}</p>
                    </TooltipContent>
                  </Tooltip>
                );
              })}
            </TooltipProvider>
          </div>
        )}

        {!isInThread && (
          <>
            {/* Only show reply count when there are actual replies */}
            {hasReplies && (
              <div className="mt-1 flex justify-between items-center">
                <div>
                  <div className="flex items-center text-xs reply-count-text cursor-pointer hover:opacity-80" onClick={onClick}>
                    <MessageCircle size={14} className="mr-1" />
                    <span>{replyCount} {replyCount === 1 ? 'reply' : 'replies'}</span>
                  </div>
                </div>
                <div className={`${showActionButtons ? 'opacity-100' : 'opacity-0'} group-hover:opacity-100 transition-opacity duration-150 flex items-center space-x-2 ml-auto`}>
                  <DefaultEmojiBar onEmojiSelect={handleEmojiSelect} workspaceSettings={workspace?.settings} className="opacity-80 hover:opacity-100" />
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <EmojiPicker onEmojiSelect={handleEmojiSelect} workspaceSettings={workspace?.settings} open={emojiPickerOpen} onOpenChange={setEmojiPickerOpen}>
                          <button onClick={(e) => { e.stopPropagation(); setEmojiPickerOpen(!emojiPickerOpen); }} className="flex items-center text-xs text-[var(--app-secondary-text)] hover:text-[var(--app-main-text)] p-1 rounded hover:bg-[var(--app-hover-bg)]" aria-label="More reactions">
                            <SmilePlus size={16} />
                          </button>
                        </EmojiPicker>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="text-xs p-1.5">More reactions</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
